#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Professional GUI for Pile Integrity Analyzer (GZ Method)
桩基完整性分析系统专业GUI (GZ方法)

This GUI provides a commercial-grade interface for:
- Traditional pile integrity analysis using GZ method
- AI-enhanced analysis with machine learning
- Comparative analysis between methods
- Advanced visualization and reporting
- Model training and configuration

Author: Pile Integrity Analysis System (GZ Method)
Version: 1.2 (Roman Numeral AI Output)
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import queue
import os
import sys
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
from matplotlib.ticker import MaxNLocator
import matplotlib.colors as mcolors
import matplotlib.font_manager as fm
import seaborn as sns
from datetime import datetime
import time
import traceback # Added for better error logging

# Import AI and ML libraries for built-in AI functionality
import pickle
import joblib
from sklearn.ensemble import IsolationForest, RandomForestClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

# --- Font Configuration for Chinese Characters ---
def configure_chinese_fonts():
    """
    Configure matplotlib to properly display Chinese characters.
    Tries multiple Chinese fonts and provides fallback options.
    """
    # List of Chinese fonts to try, in order of preference
    chinese_fonts = [
        'Microsoft YaHei',  # 微软雅黑 (common on Windows)
        'SimHei',           # 黑体 (common on Windows)
        'SimSun',           # 宋体 (common on Windows)
        'KaiTi',            # 楷体 (common on Windows)
        'FangSong',         # 仿宋 (common on Windows)
        'PingFang SC',      # 苹方 (common on macOS)
        'Hiragino Sans GB', # 冬青黑体 (common on macOS)
        'WenQuanYi Micro Hei', # 文泉驿微米黑 (common on Linux)
        'Noto Sans CJK SC', # Google Noto (cross-platform)
        'Source Han Sans SC' # Adobe Source Han Sans (cross-platform)
    ]

    available_fonts = [f.name for f in fm.fontManager.ttflist]
    selected_font = None
    for font in chinese_fonts:
        if font in available_fonts:
            selected_font = font
            break

    if selected_font:
        try:
            plt.rcParams['font.sans-serif'] = [selected_font] + plt.rcParams['font.sans-serif']
            plt.rcParams['axes.unicode_minus'] = False
            test_fig, test_ax = plt.subplots(figsize=(1, 1))
            test_ax.text(0.5, 0.5, '测试', fontsize=12, ha='center', va='center')
            plt.close(test_fig)
            print(f"成功配置中文字体: {selected_font}")
            return True
        except Exception as e:
            print(f"警告: 配置字体 {selected_font} 时出错: {e}")

    print("警告: 未找到可用的中文字体。图表中的中文可能显示为方块。")
    print("建议安装以下字体之一以获得更好的中文显示效果:")
    for font in chinese_fonts[:5]:
        print(f"  - {font}")
    try:
        plt.rcParams['axes.unicode_minus'] = False
    except:
        pass
    return False

# --- GZ Method Core Calculation Logic ---
DEFAULT_GZ_CONFIG = {
    'Sp_conditions': {
        'ge_100': lambda sp: sp >= 100, '85_lt_100': lambda sp: 85 <= sp < 100,
        '75_lt_85': lambda sp: 75 <= sp < 85, '65_lt_75': lambda sp: 65 <= sp < 75,
        'lt_65': lambda sp: sp < 65, 'ge_85': lambda sp: sp >= 85,
        'ge_75': lambda sp: sp >= 75, 'ge_65': lambda sp: sp >= 65,
    },
    'Ad_conditions': {
        'le_0': lambda ad: ad <= 0, 'gt_0_le_4': lambda ad: 0 < ad <= 4,
        'gt_4_le_8': lambda ad: 4 < ad <= 8, 'gt_8_le_12': lambda ad: 8 < ad <= 12,
        'gt_12': lambda ad: ad > 12, 'le_4': lambda ad: ad <=4,
        'le_8': lambda ad: ad <=8, 'le_12': lambda ad: ad <=12,
    },
    'Bi_ratio_conditions': {
        'gt_08': lambda br: br > 0.8, 'gt_05_le_08': lambda br: 0.5 < br <= 0.8,
        'gt_05': lambda br: br > 0.5, 'gt_025_le_05': lambda br: 0.25 < br <= 0.5,
        'gt_025': lambda br: br > 0.25, 'le_025': lambda br: br <= 0.25,
    }
}

def calculate_I_ji(Sp, Ad, Bi_ratio, config=DEFAULT_GZ_CONFIG):
    sp_cond = config['Sp_conditions']
    ad_cond = config['Ad_conditions']
    br_cond = config['Bi_ratio_conditions']
    if br_cond['gt_08'](Bi_ratio):
        if sp_cond['ge_100'](Sp) and ad_cond['le_0'](Ad): return 1
        if sp_cond['85_lt_100'](Sp) and ad_cond['le_0'](Ad): return 1
        if sp_cond['ge_100'](Sp) and ad_cond['gt_0_le_4'](Ad): return 1
    if br_cond['gt_05_le_08'](Bi_ratio) and \
       sp_cond['85_lt_100'](Sp) and ad_cond['gt_0_le_4'](Ad): return 2
    if br_cond['gt_05'](Bi_ratio):
        if sp_cond['75_lt_85'](Sp) and ad_cond['le_4'](Ad): return 2
        if sp_cond['ge_85'](Sp) and ad_cond['gt_4_le_8'](Ad): return 2
    if br_cond['gt_025_le_05'](Bi_ratio) and \
       sp_cond['75_lt_85'](Sp) and ad_cond['gt_4_le_8'](Ad): return 3
    if br_cond['gt_025'](Bi_ratio):
        if sp_cond['65_lt_75'](Sp) and ad_cond['le_8'](Ad): return 3
        if sp_cond['ge_75'](Sp) and ad_cond['gt_8_le_12'](Ad): return 3
    if br_cond['le_025'](Bi_ratio):
        if sp_cond['65_lt_75'](Sp) and ad_cond['gt_8_le_12'](Ad): return 4
        if sp_cond['lt_65'](Sp) and ad_cond['le_12'](Ad): return 4
        if sp_cond['ge_65'](Sp) and ad_cond['gt_12'](Ad): return 4
    if br_cond['gt_08'](Bi_ratio):
        if (sp_cond['75_lt_85'](Sp) and ad_cond['le_4'](Ad)) or \
           (sp_cond['ge_85'](Sp) and ad_cond['gt_4_le_8'](Ad)): return 2
        if (sp_cond['65_lt_75'](Sp) and ad_cond['le_8'](Ad)) or \
           (sp_cond['ge_75'](Sp) and ad_cond['gt_8_le_12'](Ad)): return 3
        if (sp_cond['lt_65'](Sp)) or (ad_cond['gt_12'](Ad)): return 4
    if Bi_ratio <= 0.25: return 4
    if 0.25 < Bi_ratio <= 0.5: return 3
    if 0.5 < Bi_ratio <= 0.8: return 2
    print(f"Warning: Unclassified I(j,i) for Sp={Sp}, Ad={Ad}, Bi_ratio={Bi_ratio}. Defaulting to 2.")
    return 2

def calculate_K_i(I_ji_values_at_depth):
    if not I_ji_values_at_depth: return 0
    valid_I_ji = [i_val for i_val in I_ji_values_at_depth if i_val in [1, 2, 3, 4]]
    if not valid_I_ji: return 0
    sum_I_ji_sq = sum(i_val**2 for i_val in valid_I_ji)
    sum_I_ji = sum(valid_I_ji)
    if sum_I_ji == 0: return 0
    K_i_float = (sum_I_ji_sq / sum_I_ji) + 0.5
    return int(K_i_float)

def check_consecutive_K(K_values_with_depths, target_K, num_consecutive=6, depth_interval=0.1):
    if num_consecutive <= 0: return False, -1
    sorted_k_data = sorted(K_values_with_depths.items())
    if len(sorted_k_data) < num_consecutive: return False, -1
    for i in range(len(sorted_k_data) - num_consecutive + 1):
        window = sorted_k_data[i : i + num_consecutive]
        all_target_K = all(item[1] == target_K for item in window)
        if not all_target_K: continue
        start_depth_of_window = window[0][0]
        end_depth_of_window = window[-1][0]
        actual_span = end_depth_of_window - start_depth_of_window
        expected_span = (num_consecutive - 1) * depth_interval
        if abs(actual_span - expected_span) < (depth_interval / 2.0):
            return True, start_depth_of_window
    return False, -1

def determine_final_category(K_values_map_with_depths):
    report_details = []
    if not K_values_map_with_depths:
        return "N/A", ["没有计算K值。"]
    K_values_list = list(K_values_map_with_depths.values())
    has_K4 = any(k == 4 for k in K_values_list)
    has_K3 = any(k == 3 for k in K_values_list)
    has_K2 = any(k == 2 for k in K_values_list)

    if has_K4:
        report_details.append("桩身存在K(i)=4的检测横截面。")
        return "IV类桩", report_details
    consecutive_K3_found, k3_start_depth = check_consecutive_K(K_values_map_with_depths, target_K=3, num_consecutive=6)
    if consecutive_K3_found:
        report_details.append(f"在深度 {k3_start_depth:.2f}m 开始的约50cm范围内K(i)值均为3。")
        return "IV类桩", report_details
    if has_K3:
        num_K3 = K_values_list.count(3)
        if num_K3 == 1:
            report_details.append("所有检测截面仅存在一个K(i)=3的情况，且不存在Ki=4。")
            return "III类桩", report_details
        if num_K3 > 1:
            k3_depths = sorted([d for d, k in K_values_map_with_depths.items() if k == 3])
            adjacent_k3_too_close = False
            for i in range(len(k3_depths) - 1):
                if (k3_depths[i+1] - k3_depths[i]) < 0.5:
                    adjacent_k3_too_close = True
                    report_details.append(f"存在相邻K(i)=3的截面距离小于50cm (例如深度 {k3_depths[i]:.2f}m 和 {k3_depths[i+1]:.2f}m)。")
                    break
            if not adjacent_k3_too_close:
                 report_details.append("所有检测截面存在多个K(i)=3，无Ki=4，且任意两个相邻Ki=3截面距离≥50cm。")
                 return "III类桩", report_details
            else:
                 report_details.append("存在多个K(i)=3，无Ki=4，但部分相邻Ki=3截面距离<50cm (未形成IV类条件)。") # This could still be III if not meeting IV criteria
                 return "III类桩", report_details # Keeping as III as per original logic unless IV conditions are met
        report_details.append("桩身存在K(i)=3的检测横截面 (未满足IV类条件，且不符合特定III类细则)。")
        return "III类桩", report_details
    consecutive_K2_found, k2_start_depth = check_consecutive_K(K_values_map_with_depths, target_K=2, num_consecutive=6)
    if consecutive_K2_found and not has_K3 and not has_K4:
        report_details.append(f"在深度 {k2_start_depth:.2f}m 开始的约50cm范围内K(i)值均为2，且无Ki=3, Ki=4。")
        return "III类桩", report_details # This was III类桩 in original logic for this specific K2 case
    if has_K2 and not has_K3 and not has_K4:
        num_K2 = K_values_list.count(2)
        if num_K2 == 1:
            report_details.append("所有检测截面仅存在一个K(i)=2，且无Ki=3, Ki=4。")
            return "II类桩", report_details
        if num_K2 > 1: # And not meeting the consecutive K2 for III类桩 condition
            report_details.append("所有检测截面存在多个K(i)=2，无Ki=3, Ki=4，且不存在某深度50cm范围内K(i)值均为2。")
            return "II类桩", report_details
    if all(k == 1 for k in K_values_list):
        report_details.append("桩身各检测横截面完整性类别指数K(i)均为1。")
        return "I类桩", report_details
    report_details.append("未能明确分类，或数据不满足任何明确的I-IV类桩条件。请检查K值分布。")
    return "未定类别", report_details

# --- Built-in AI Analysis Engine ---
class BuiltInAIAnalyzer:
    def __init__(self, config=None):
        self.config = config or { # This config seems related to feature engineering, not direct pile categories
            '正常': {'speed': (90.0, 1000.0), 'amp': (-100, 3)},
            '轻微畸变': {'speed': (80.0, 90.0), 'amp': (3, 6)},
            '明显畸变': {'speed': (70.0, 80.0), 'amp': (6, 12)},
            '严重畸变': {'speed': (0.0, 70.0), 'amp': (12, 100)},
            'continuous_threshold': 0.5
        }
        self.classifier_model = None
        self.anomaly_detector = None
        self.scaler = None
        self.feature_importance = {}
        self.training_data = [] # Stores (features, labels) tuples
        self.feature_extractor = None
        self.feature_selector = None
        self.preprocessor = None
        self.is_optimized_model = False
        self.loaded_model_path = None

        # Universal Roman Numeral Mapping for AI output
        self.AI_ROMAN_MAP = {
            0: 'I', 1: 'II', 2: 'III', 3: 'IV',  # Numeric input
            '0': 'I', '1': 'II', '2': 'III', '3': 'IV', # String numeric input
            'I': 'I', 'II': 'II', 'III': 'III', 'IV': 'IV', # Roman input (idempotent)
            'I类桩': 'I', 'II类桩': 'II', 'III类桩': 'III', 'IV类桩': 'IV' # Handle if model somehow outputs Chinese
        }
        self.AI_DEFAULT_CATEGORY_ROMAN = 'IV' # Default category if mapping fails

        self._initialize_models()
        self._initial_training()


    def _initialize_models(self):
        try:
            self.classifier_model = RandomForestClassifier(n_estimators=100, max_depth=10, random_state=42, class_weight='balanced')
            self.anomaly_detector = IsolationForest(contamination=0.1, random_state=42)
            self.scaler = StandardScaler()
            print("✅ AI models initialized successfully")
        except Exception as e:
            print(f"❌ Failed to initialize AI models: {str(e)}")

    def _initial_training(self):
        try:
            print("🔧 Performing initial AI model training...")
            success = self.train_models() # Train with synthetic data
            if success:
                print("✅ Initial AI model training completed")
            else:
                print("⚠️ Initial AI model training failed, will train on first use")
        except Exception as e:
            print(f"⚠️ Initial training error: {str(e)}, will train on first use")

    def _standardize_column_names(self, df_orig):
        df = df_orig.copy()
        column_mapping = {
            'Depth(m)': 'Depth', '1-2 Speed%': 'S1', '1-2 Amp%': 'A1',
            '1-3 Speed%': 'S2', '1-3 Amp%': 'A2', '2-3 Speed%': 'S3', '2-3 Amp%': 'A3'
        }
        df.rename(columns=column_mapping, inplace=True)
        return df

    def extract_features(self, data_df_orig):
        """
        Extracts features from pile integrity data.
        Prioritizes using self.feature_extractor if an optimized model is loaded.
        Otherwise, falls back to standard GUI feature extraction.
        """
        try:
            data_df = self._standardize_column_names(data_df_orig.copy())

            if hasattr(self, 'is_optimized_model') and self.is_optimized_model and \
               hasattr(self, 'feature_extractor') and self.feature_extractor is not None:
                print("🔧 Using model's specific feature_extractor.")
                if hasattr(self.feature_extractor, 'extract_features') and callable(self.feature_extractor.extract_features):
                    extracted_features, feature_names = self.feature_extractor.extract_features(data_df)
                    if extracted_features.ndim == 1: extracted_features = extracted_features.reshape(1, -1)
                    print(f"✅ Model's feature_extractor produced: {extracted_features.shape[1]} features.")
                    return extracted_features, feature_names
                elif hasattr(self.feature_extractor, 'transform') and callable(self.feature_extractor.transform):
                    print("🔧 Model's feature_extractor is a transformer. Applying transform.")
                    extracted_features = self.feature_extractor.transform(data_df)
                    if extracted_features.ndim == 1: extracted_features = extracted_features.reshape(1, -1)
                    feature_names = [f"feature_{i}" for i in range(extracted_features.shape[1])]
                    if hasattr(self.feature_extractor, 'get_feature_names_out'):
                        try: feature_names = self.feature_extractor.get_feature_names_out()
                        except: pass
                    elif hasattr(self.feature_extractor, 'feature_names'):
                        feature_names = self.feature_extractor.feature_names
                    print(f"✅ Model's transformer feature_extractor produced: {extracted_features.shape[1]} features.")
                    return extracted_features, feature_names
                else:
                    print(f"⚠️ Model's feature_extractor does not have a recognized interface. Falling back.")
            
            print("🔧 Using standard GUI feature extractor (54 features).")
            features = []
            feature_names = []
            for col in ['S1', 'S2', 'S3', 'A1', 'A2', 'A3']:
                if col in data_df.columns:
                    values = data_df[col].dropna()
                    if len(values) > 0:
                        features.extend([values.mean(), values.std(), values.min(), values.max(), values.median()])
                        feature_names.extend([f'{col}_mean', f'{col}_std', f'{col}_min', f'{col}_max', f'{col}_median'])
            
            speed_cols = ['S1', 'S2', 'S3']
            speed_data = data_df[speed_cols].dropna()
            if not speed_data.empty:
                avg_speed = speed_data.mean(axis=1)
                features.extend([
                    avg_speed.mean(), avg_speed.std(),
                    (avg_speed < 70).sum() / len(avg_speed) if len(avg_speed) > 0 else 0,
                    (avg_speed < 80).sum() / len(avg_speed) if len(avg_speed) > 0 else 0,
                    (avg_speed < 90).sum() / len(avg_speed) if len(avg_speed) > 0 else 0
                ])
                feature_names.extend(['avg_speed_mean', 'avg_speed_std', 'severe_speed_ratio', 'obvious_speed_ratio', 'light_speed_ratio'])

            amp_cols = ['A1', 'A2', 'A3']
            amp_data = data_df[amp_cols].dropna()
            if not amp_data.empty:
                avg_amp = amp_data.mean(axis=1)
                features.extend([
                    avg_amp.mean(), avg_amp.std(),
                    (avg_amp > 12).sum() / len(avg_amp) if len(avg_amp) > 0 else 0,
                    (avg_amp > 8).sum() / len(avg_amp) if len(avg_amp) > 0 else 0,
                    (avg_amp > 6).sum() / len(avg_amp) if len(avg_amp) > 0 else 0
                ])
                feature_names.extend(['avg_amp_mean', 'avg_amp_std', 'severe_amp_ratio', 'obvious_amp_ratio', 'light_amp_ratio'])

            if 'Depth' in data_df.columns and not data_df['Depth'].empty:
                depth_range = data_df['Depth'].max() - data_df['Depth'].min()
                features.extend([data_df['Depth'].min(), data_df['Depth'].max(), depth_range, len(data_df)])
                feature_names.extend(['depth_min', 'depth_max', 'depth_range', 'num_points'])

            target_feature_count = 54
            if len(speed_cols) > 0 and len(amp_cols) > 0:
                for i, speed_col in enumerate(speed_cols):
                    for j, amp_col in enumerate(amp_cols):
                        if speed_col in data_df.columns and amp_col in data_df.columns:
                            corr = data_df[speed_col].corr(data_df[amp_col])
                            features.append(corr if not np.isnan(corr) else 0.0)
                            feature_names.append(f'{speed_col}_{amp_col}_corr')
            for col in ['S1', 'S2', 'S3', 'A1', 'A2', 'A3']:
                if col in data_df.columns and 'Depth' in data_df.columns and len(data_df['Depth']) > 1 and len(data_df[col]) > 1:
                    try:
                        valid_indices = data_df['Depth'].notna() & data_df[col].notna()
                        if sum(valid_indices) > 1:
                             slope = np.polyfit(data_df.loc[valid_indices, 'Depth'], data_df.loc[valid_indices, col], 1)[0]
                             features.append(slope)
                        else: features.append(0.0)
                    except Exception: features.append(0.0)
                    feature_names.append(f'{col}_trend')
                elif len(features) < target_feature_count :
                    features.append(0.0)
                    feature_names.append(f'{col}_trend_fallback')

            remaining_features = target_feature_count - len(features)
            if remaining_features > 0:
                features.extend([0.0] * remaining_features)
                feature_names.extend([f'padding_{i}' for i in range(remaining_features)])
            
            final_features = np.array(features[:target_feature_count]).reshape(1, -1)
            final_feature_names = feature_names[:target_feature_count]
            print(f"✅ Standard GUI feature_extractor produced: {final_features.shape[1]} features.")
            return final_features, final_feature_names
        except Exception as e:
            print(f"❌ Feature extraction error: {str(e)}"); traceback.print_exc()
            return np.array([]).reshape(1, -1), []

    def generate_synthetic_training_data(self, num_samples=1000):
        try:
            training_features = []
            training_labels = []
            # For synthetic training, we can use numeric labels (0-3) or string labels like 'I类桩'
            # The prediction logic will handle mapping these to Roman numerals.
            # Let's use 'I类桩' etc. for clarity in synthetic data generation, as it matches GZ.
            categories_gz = ['I类桩', 'II类桩', 'III类桩', 'IV类桩']
            num_speed_features = 15; num_amp_features = 15
            num_other_features = 54 - num_speed_features - num_amp_features

            for category_gz in categories_gz:
                for _ in range(num_samples // len(categories_gz)):
                    if category_gz == 'I类桩':
                        speed_features = np.random.normal(95, 5, num_speed_features)
                        amp_features = np.random.normal(2, 1, num_amp_features)
                    elif category_gz == 'II类桩':
                        speed_features = np.random.normal(85, 5, num_speed_features)
                        amp_features = np.random.normal(5, 2, num_amp_features)
                    elif category_gz == 'III类桩':
                        speed_features = np.random.normal(75, 5, num_speed_features)
                        amp_features = np.random.normal(9, 3, num_amp_features)
                    else:  # IV类桩
                        speed_features = np.random.normal(60, 10, num_speed_features)
                        amp_features = np.random.normal(15, 5, num_amp_features)
                    
                    other_features_array = np.random.normal(0, 1, num_other_features)
                    features = np.concatenate([speed_features, amp_features, other_features_array])
                    training_features.append(features)
                    training_labels.append(category_gz) # Store GZ-style labels for synthetic training
            
            self.training_data = list(zip(training_features, training_labels))
            print(f"✅ Generated {len(training_features)} synthetic training samples with {len(training_features[0])} features each.")
            return np.array(training_features), np.array(training_labels)
        except Exception as e:
            print(f"❌ Synthetic data generation error: {str(e)}")
            return np.array([]), np.array([])

    def train_models(self, features=None, labels=None):
        try:
            if features is None or labels is None or len(features) == 0:
                print("🔧 No explicit training data provided, generating synthetic data for training.")
                features, labels = self.generate_synthetic_training_data()

            if len(features) == 0:
                print("❌ No training data available, cannot train models.")
                return False

            print(f"🏋️ Training models with data of shape: {features.shape}")
            
            try: features_scaled = self.scaler.fit_transform(features)
            except Exception as scale_fit_error:
                print(f"❌ Error fitting scaler: {scale_fit_error}. Re-initializing scaler.")
                self.scaler = StandardScaler(); features_scaled = self.scaler.fit_transform(features)

            # Labels for training can be 'I类桩', 'II类桩', etc., or numeric 0-3.
            # The model will learn these. The prediction output mapping is separate.
            self.classifier_model.fit(features_scaled, labels)
            self.anomaly_detector.fit(features_scaled)

            if hasattr(self.classifier_model, 'feature_importances_'):
                num_feats = features.shape[1]
                feature_names_for_importance = [f'feature_{i}' for i in range(num_feats)]
                self.feature_importance = dict(zip(feature_names_for_importance, self.classifier_model.feature_importances_))
            
            print("✅ AI models trained successfully")
            return True
        except Exception as e:
            print(f"❌ Model training error: {str(e)}"); traceback.print_exc()
            return False

    def _map_value_to_roman(self, value):
        """Helper to map various inputs to Roman numeral string I, II, III, IV or default."""
        if isinstance(value, (int, float)):
            return self.AI_ROMAN_MAP.get(int(value), self.AI_DEFAULT_CATEGORY_ROMAN)
        if isinstance(value, str):
            return self.AI_ROMAN_MAP.get(value.strip(), self.AI_DEFAULT_CATEGORY_ROMAN)
        return self.AI_DEFAULT_CATEGORY_ROMAN


    def predict(self, data_df):
        try:
            print(f"🤖 AI Predict called with data_df of shape: {data_df.shape}")
            features, feature_names = self.extract_features(data_df)
            if features.size == 0:
                print("❌ No features extracted from data_df in predict method.")
                return None
            print(f"⚙️ Features extracted for prediction, shape: {features.shape}")

            if hasattr(self, 'is_optimized_model') and self.is_optimized_model:
                print("✨ Routing to optimized model prediction logic.")
                return self._predict_with_optimized_model_internal(features, data_df) 
            else:
                print("🔧 Routing to standard model prediction logic.")
                if self.classifier_model is None or not hasattr(self.classifier_model, 'classes_'):
                    print("⚠️ Standard classifier model not trained. Attempting to train.")
                    if not self.train_models():
                        print("❌ Failed to train standard model, cannot predict.")
                        return None
                
                try:
                    if not hasattr(self.scaler, 'scale_') or self.scaler.scale_ is None:
                        print("⚠️ Standard scaler not fitted. This should have been handled by train_models.")
                        # Assuming train_models ensures scaler is fit. If not, an error here is indicative of a deeper issue.
                    features_scaled = self.scaler.transform(features)
                except Exception as scale_error:
                    print(f"⚠️ Scaler error: {scale_error}. Retraining as fallback.")
                    if not self.train_models():
                         print("❌ Failed to retrain models after scaler error.")
                         return None
                    features_scaled = self.scaler.transform(features)

                prediction_raw = self.classifier_model.predict(features_scaled)[0]
                probabilities = self.classifier_model.predict_proba(features_scaled)[0]
                class_names_raw = self.classifier_model.classes_

                # Convert raw prediction to Roman numeral 'I', 'II', 'III', 'IV'
                predicted_category_roman = self._map_value_to_roman(prediction_raw)

                class_probabilities_mapped_roman = {}
                for i, class_label_raw in enumerate(class_names_raw):
                    class_name_roman = self._map_value_to_roman(class_label_raw)
                    # Aggregate probabilities if multiple raw labels map to the same Roman numeral
                    class_probabilities_mapped_roman[class_name_roman] = \
                        class_probabilities_mapped_roman.get(class_name_roman, 0) + probabilities[i]

                anomaly_score = self.anomaly_detector.decision_function(features_scaled)[0]
                is_anomaly = self.anomaly_detector.predict(features_scaled)[0] == -1
                
                # Confidence is the probability of the predicted Roman numeral category
                confidence = class_probabilities_mapped_roman.get(predicted_category_roman, 0.0)

                result = {
                    '完整性类别': predicted_category_roman, # Store as Roman numeral
                    'ai_confidence': confidence,
                    'anomaly_score': anomaly_score,
                    'is_anomaly': is_anomaly,
                    'class_probabilities': class_probabilities_mapped_roman, # Keys are Roman
                    'feature_importance': self.feature_importance,
                    'overall_reasoning': self._generate_reasoning(predicted_category_roman, confidence, anomaly_score, class_probabilities_mapped_roman)
                }
                print(f"🎯 标准模型预测结果: {predicted_category_roman} | 置信度: {confidence:.2%}")
                return result
        except Exception as e:
            print(f"❌ Prediction error in main predict method: {str(e)}"); traceback.print_exc()
            return None

    def _predict_with_optimized_model_internal(self, features, data_df_for_preprocessor=None):
        try:
            print(f"🚀 Inside _predict_with_optimized_model_internal with features shape: {features.shape}")
            final_features_for_model = None

            if hasattr(self, 'scaler') and self.scaler and \
               hasattr(self, 'feature_selector') and self.feature_selector:
                print("🚀 Applying model's scaler and feature_selector.")
                if not hasattr(self.scaler, 'mean_') or self.scaler.mean_ is None:
                     print("CRITICAL ERROR: Optimized model's scaler is not fitted."); return None
                features_scaled = self.scaler.transform(features)
                features_selected = self.feature_selector.transform(features_scaled)
                final_features_for_model = features_selected
            elif hasattr(self, 'preprocessor') and self.preprocessor:
                print("🚀 Applying model's preprocessor.")
                final_features_for_model = self.preprocessor.transform(features)
            elif hasattr(self, 'scaler') and self.scaler:
                print("🚀 Applying model's scaler only.")
                if not hasattr(self.scaler, 'mean_') or self.scaler.mean_ is None:
                     print("CRITICAL ERROR: Optimized model's scaler is not fitted."); return None
                final_features_for_model = self.scaler.transform(features)
            else:
                print("🚀 Using features directly for optimized model.")
                final_features_for_model = features
            
            print(f"⚙️ Final features for model, shape: {final_features_for_model.shape}")
            if final_features_for_model is None or self.classifier_model is None:
                print("CRITICAL ERROR: Features or classifier_model is None."); return None

            prediction_raw = self.classifier_model.predict(final_features_for_model)[0]
            probabilities = self.classifier_model.predict_proba(final_features_for_model)[0]
            class_names_raw = self.classifier_model.classes_

            predicted_category_roman = self._map_value_to_roman(prediction_raw)

            class_probabilities_mapped_roman = {}
            for i, class_label_raw in enumerate(class_names_raw):
                class_name_roman = self._map_value_to_roman(class_label_raw)
                class_probabilities_mapped_roman[class_name_roman] = \
                    class_probabilities_mapped_roman.get(class_name_roman, 0) + probabilities[i]
            
            anomaly_score = -0.05 # Placeholder for optimized models if not explicitly calculated
            is_anomaly = False    
            confidence = class_probabilities_mapped_roman.get(predicted_category_roman, 0.0)
            
            current_feature_importance = {}
            if hasattr(self.classifier_model, 'feature_importances_'):
                num_model_features = len(self.classifier_model.feature_importances_)
                if len(self.feature_importance) == num_model_features:
                     current_feature_importance = self.feature_importance
                else:
                    current_feature_importance = {f"model_feat_{i}": imp for i, imp in enumerate(self.classifier_model.feature_importances_)}
            
            result = {
                '完整性类别': predicted_category_roman, # Store as Roman numeral
                'ai_confidence': confidence,
                'anomaly_score': anomaly_score,
                'is_anomaly': is_anomaly,
                'class_probabilities': class_probabilities_mapped_roman, # Keys are Roman
                'feature_importance': current_feature_importance, 
                'overall_reasoning': self._generate_reasoning(predicted_category_roman, confidence, anomaly_score, class_probabilities_mapped_roman)
            }
            print(f"🎯 Optimized model prediction: {predicted_category_roman} with {confidence:.2%} confidence")
            return result
        except Exception as e:
            print(f"❌ Optimized model internal prediction error: {str(e)}"); traceback.print_exc()
            return None

    def _generate_reasoning(self, prediction_roman, confidence, anomaly_score, class_probabilities_roman):
        # prediction_roman is 'I', 'II', 'III', 'IV'
        # class_probabilities_roman keys are 'I', 'II', 'III', 'IV'
        reasoning = f"AI分析结果：{prediction_roman}\n\n" # Display Roman numeral
        reasoning += f"置信度分析：\n- 主要预测置信度：{confidence:.2%}\n"
        if confidence > 0.8: reasoning += "- 置信度较高，预测结果可信\n"
        elif confidence > 0.6: reasoning += "- 置信度中等，建议结合传统方法\n"
        else: reasoning += "- 置信度较低，建议以传统方法为准\n"
        reasoning += f"\n异常检测：\n- 异常分数：{anomaly_score:.3f}\n"
        if anomaly_score < -0.1: reasoning += "- 检测到异常模式，需要特别关注\n"
        else: reasoning += "- 数据模式正常\n"
        reasoning += f"\n各类别概率分布：\n"
        for class_name_roman, prob in sorted(class_probabilities_roman.items(), key=lambda x: x[1], reverse=True):
            reasoning += f"- {class_name_roman}：{prob:.2%}\n" # Display Roman numeral
        return reasoning

    def save_models(self, filepath):
        try:
            model_data = {
                'classifier_model': self.classifier_model,
                'anomaly_detector': self.anomaly_detector,
                'scaler': self.scaler,
                'feature_importance': self.feature_importance,
                'config': self.config,
                'feature_extractor': self.feature_extractor if hasattr(self, 'feature_extractor') else None,
                'feature_selector': self.feature_selector if hasattr(self, 'feature_selector') else None,
                'preprocessor': self.preprocessor if hasattr(self, 'preprocessor') else None,
                'is_optimized_model': self.is_optimized_model
            }
            with open(filepath, 'wb') as f: pickle.dump(model_data, f)
            print(f"✅ Models saved to {filepath}")
            return True
        except Exception as e:
            print(f"❌ Failed to save models: {str(e)}"); return False

    def load_models(self, filepath):
        try:
            with open(filepath, 'rb') as f: model_data = pickle.load(f)
            print(f"📦 Loaded model_data type: {type(model_data)}")
            if isinstance(model_data, dict): print(f"📦 model_data keys: {list(model_data.keys())}")

            self.is_optimized_model = False; self.feature_extractor = None
            self.feature_selector = None; self.preprocessor = None
            self.scaler = StandardScaler(); self.anomaly_detector = IsolationForest(contamination=0.1, random_state=42)
            self.feature_importance = {}

            if isinstance(model_data, dict):
                self.classifier_model = model_data.get('classifier_model', model_data.get('model'))
                loaded_scaler = model_data.get('scaler')
                if loaded_scaler: self.scaler = loaded_scaler
                loaded_anomaly_detector = model_data.get('anomaly_detector')
                if loaded_anomaly_detector: self.anomaly_detector = loaded_anomaly_detector
                self.feature_importance = model_data.get('feature_importance', {})
                self.config = model_data.get('config', self.config)

                if model_data.get('is_optimized_model'): self.is_optimized_model = True; print("🚩 is_optimized_model flag found.")
                elif 'feature_extractor' in model_data or 'preprocessor' in model_data : self.is_optimized_model = True; print("🚩 Inferred is_optimized_model.")

                if self.is_optimized_model:
                    self.feature_extractor = model_data.get('feature_extractor')
                    self.feature_selector = model_data.get('feature_selector')
                    self.preprocessor = model_data.get('preprocessor')
                    if self.feature_extractor: print("Loaded model's feature_extractor.")
                    if self.feature_selector: print("Loaded model's feature_selector.")
                    if self.preprocessor: print("Loaded model's preprocessor.")
                    if self.preprocessor is None and loaded_scaler is None: print("⚠️ Optimized model loaded but no specific scaler/preprocessor.")
                    elif loaded_scaler: print("Loaded model's scaler.")

                if self.classifier_model is None: print(f"❌ Critical: No classifier model found in .pkl."); return False
                print(f"✅ Model package loaded. is_optimized_model: {self.is_optimized_model}")
            elif hasattr(model_data, 'predict'):
                self.classifier_model = model_data; self.is_optimized_model = False 
                print(f"✅ Single classifier model loaded. Treating as standard.")
                self._initialize_missing_components_for_standard_model()
            else:
                print(f"❌ Unknown model format in {filepath}: {type(model_data)}"); return False
            
            self.loaded_model_path = filepath
            return True
        except Exception as e:
            print(f"❌ Failed to load models from {filepath}: {str(e)}"); traceback.print_exc(); return False

    def _initialize_missing_components_for_standard_model(self):
        try:
            if not hasattr(self.scaler, 'scale_') or self.scaler.scale_ is None or \
               not hasattr(self.anomaly_detector, 'estimators_features_'):
                print("🔧 Standard model: components (scaler/anomaly) might need fitting. Using synthetic data.")
                features_syn, _ = self.generate_synthetic_training_data(100)
                if features_syn.size > 0:
                    if not hasattr(self.scaler, 'scale_') or self.scaler.scale_ is None:
                        self.scaler.fit(features_syn); print("✅ Scaler fitted (synthetic).")
                    if not hasattr(self.anomaly_detector, 'estimators_features_'):
                        scaled_features_syn = self.scaler.transform(features_syn)
                        self.anomaly_detector.fit(scaled_features_syn); print("✅ Anomaly detector fitted (synthetic).")
        except Exception as e: print(f"⚠️ Error initializing missing components for standard model: {e}")

    def _collect_real_training_data(self): pass


# --- GUI Class ---
class PileAnalyzerGZGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Pile Integrity Analyzer (GZ Method) - 桩基完整性分析系统 (GZ方法)")
        self.root.geometry("1600x1000"); self.root.minsize(1200, 800)
        self.root.configure(bg='#f8f9fa'); self.root.resizable(True, True)
        self.root.state('normal'); self.root.lift(); self.root.attributes('-topmost', True)
        self.root.after_idle(lambda: self.root.attributes('-topmost', False))
        self.center_window(); self.setup_window_controls(); self.setup_styles()

        self.ai_models_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ai_models')
        os.makedirs(self.ai_models_dir, exist_ok=True)
        
        self.ai_analyzer = BuiltInAIAnalyzer()
        
        try:
            from ai_analyzer_v2 import get_ai_analyzer_v2 
            self.ai_analyzer_v2 = get_ai_analyzer_v2(); self.use_v2_analyzer = True
            print("✅ AI分析器 V2.0 已启用")
        except ImportError:
            print("⚠️ ai_analyzer_v2.py 未找到或导入失败。AI分析器 V2.0 将不可用。")
            self.ai_analyzer_v2 = None; self.use_v2_analyzer = False
        except Exception as e:
            print(f"⚠️ AI分析器 V2.0 启用失败，使用V1版本: {e}")
            self.ai_analyzer_v2 = None; self.use_v2_analyzer = False

        self.current_file = None; self.analysis_results = {}; self.progress_queue = queue.Queue()
        self.data_df = None; self.profiles = []; self.profile_name_map = {}

        self.config_vars = {
            'sp_ge_100': tk.DoubleVar(value=100.0), 'sp_85_lt_100_min': tk.DoubleVar(value=85.0),
            'sp_85_lt_100_max': tk.DoubleVar(value=100.0), 'sp_75_lt_85_min': tk.DoubleVar(value=75.0),
            'sp_75_lt_85_max': tk.DoubleVar(value=85.0), 'sp_65_lt_75_min': tk.DoubleVar(value=65.0),
            'sp_65_lt_75_max': tk.DoubleVar(value=75.0), 'ad_le_0': tk.DoubleVar(value=0.0),
            'ad_gt_0_le_4_min': tk.DoubleVar(value=0.0), 'ad_gt_0_le_4_max': tk.DoubleVar(value=4.0),
            'ad_gt_4_le_8_min': tk.DoubleVar(value=4.0), 'ad_gt_4_le_8_max': tk.DoubleVar(value=8.0),
            'ad_gt_8_le_12_min': tk.DoubleVar(value=8.0), 'ad_gt_8_le_12_max': tk.DoubleVar(value=12.0),
            'bi_ratio_default': tk.DoubleVar(value=1.0), 'auto_analysis': tk.BooleanVar(value=True),
            'show_details': tk.BooleanVar(value=True), 'ai_model_path': tk.StringVar(value="")
        }
        configure_chinese_fonts()
        self.setup_gui()

    def center_window(self):
        self.root.update_idletasks()
        sw, sh = self.root.winfo_screenwidth(), self.root.winfo_screenheight()
        ww, wh = 1600, 1000
        if ww > sw: ww = sw - 100
        if wh > sh: wh = sh - 100
        x, y = max(0, (sw - ww) // 2), max(0, (sh - wh) // 2)
        self.root.geometry(f"{ww}x{wh}+{x}+{y}")

    def bind_mousewheel(self, widget):
        def _on_mousewheel(event): widget.yview_scroll(int(-1*(event.delta/120)), "units")
        def _bind(event): widget.bind_all("<MouseWheel>", _on_mousewheel)
        def _unbind(event): widget.unbind_all("<MouseWheel>")
        widget.bind('<Enter>', _bind); widget.bind('<Leave>', _unbind)

    def setup_window_controls(self):
        self.root.bind('<F11>', self.toggle_fullscreen); self.root.bind('<Escape>', self.exit_fullscreen)
        self.root.bind('<Control-q>', lambda e: self.root.quit())
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing); self.is_fullscreen = False

    def toggle_fullscreen(self, event=None):
        self.is_fullscreen = not self.is_fullscreen; self.root.attributes('-fullscreen', self.is_fullscreen)
    def exit_fullscreen(self, event=None):
        self.is_fullscreen = False; self.root.attributes('-fullscreen', False)
    def on_closing(self):
        if messagebox.askokcancel("Quit", "Do you want to quit the application?"):
            self.root.quit(); self.root.destroy()

    def setup_styles(self):
        style = ttk.Style(); style.theme_use('clam')
        self.colors = {'primary': '#2c3e50', 'secondary': '#3498db', 'success': '#27ae60', 
                       'warning': '#f39c12', 'danger': '#e74c3c', 'light': '#ecf0f1', 
                       'dark': '#34495e', 'white': '#ffffff', 'accent': '#9b59b6'}
        style.configure('Title.TLabel', font=('Segoe UI', 18, 'bold'), fg=self.colors['primary'], bg='#f8f9fa')
        style.configure('Heading.TLabel', font=('Segoe UI', 12, 'bold'), fg=self.colors['dark'], bg='#f8f9fa')
        style.configure('Modern.TButton', font=('Segoe UI', 10), padding=(12, 8))
        style.configure('Accent.TButton', font=('Segoe UI', 11, 'bold'), padding=(15, 10))
        style.configure('Success.TButton', font=('Segoe UI', 10, 'bold'), padding=(12, 8))
        style.configure('Modern.TFrame', background='#f8f9fa', relief='flat')
        style.configure('Modern.TNotebook', background='#f8f9fa', borderwidth=0)
        style.configure('Modern.TNotebook.Tab', padding=(20, 12), font=('Segoe UI', 10, 'bold'))

    def create_header(self):
        hf = tk.Frame(self.root, bg=self.colors['primary'], height=80); hf.pack(fill='x', padx=0, pady=0); hf.pack_propagate(False)
        tk.Label(hf, text="🔬 Pile Integrity Analyzer (GZ Method)", font=('Segoe UI', 16, 'bold'), fg='white', bg=self.colors['primary']).pack(pady=(15, 2))
        tk.Label(hf, text="桩基完整性分析系统 (GZ方法) | Professional Engineering Analysis Solution", font=('Segoe UI', 9), fg=self.colors['light'], bg=self.colors['primary']).pack()

    def setup_gui(self):
        self.create_header()
        self.notebook = ttk.Notebook(self.root, style='Modern.TNotebook')
        self.notebook.pack(fill='both', expand=True, padx=15, pady=(5, 10))
        tabs = [("📁 Data Loading", self.setup_data_tab), ("🔬 Analysis", self.setup_analysis_tab),
                ("📊 GZ Traditional Analysis", self.setup_traditional_results_tab), ("🤖 AI Analysis", self.setup_ai_results_tab),
                ("⚖️ Comparison", self.setup_comparison_tab), ("📈 Visualization", self.setup_visualization_tab),
                ("⚙️ Configuration", self.setup_config_tab)]
        for text, setup_func in tabs: setup_func()
        self.setup_status_bar(); self.monitor_progress()

    def setup_status_bar(self):
        sf = tk.Frame(self.root, bg='#e9ecef', height=30); sf.pack(side='bottom', fill='x'); sf.pack_propagate(False)
        self.main_status_var = tk.StringVar(value="Ready")
        tk.Label(sf, textvariable=self.main_status_var, bg='#e9ecef', fg='#495057', font=('Segoe UI', 9), anchor='w').pack(side='left', padx=10, pady=5)
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(sf, variable=self.progress_var, mode='determinate', length=200)
        self.progress_bar.pack(side='right', padx=10, pady=5)

    def monitor_progress(self):
        try:
            while True:
                msg = self.progress_queue.get_nowait()
                if isinstance(msg, dict):
                    if 'status' in msg: self.main_status_var.set(msg['status'])
                    if 'progress' in msg: self.progress_var.set(msg['progress'])
                else: self.main_status_var.set(str(msg))
        except queue.Empty: pass
        finally: self.root.after(100, self.monitor_progress)

    def setup_data_tab(self):
        df = ttk.Frame(self.notebook, style='Modern.TFrame'); self.notebook.add(df, text="📁 Data Loading")
        mc = ttk.Frame(df, style='Modern.TFrame'); mc.pack(fill='both', expand=True, padx=20, pady=15)
        ttk.Label(mc, text="📁 Data Loading & Preview", style='Title.TLabel').pack(pady=(0, 20))
        ff = ttk.LabelFrame(mc, text="📂 File Selection"); ff.pack(fill='x', pady=(0, 20), padx=10)
        pf = ttk.Frame(ff); pf.pack(fill='x', padx=15, pady=15)
        ttk.Label(pf, text="Selected File:", style='Heading.TLabel').pack(anchor='w')
        self.file_path_var = tk.StringVar(value="No file selected")
        fd = tk.Frame(pf, bg='white', relief='solid', bd=1); fd.pack(fill='x', pady=5)
        self.file_label = tk.Label(fd, textvariable=self.file_path_var, bg='white', fg='#666666', font=('Segoe UI', 9), anchor='w')
        self.file_label.pack(fill='both', expand=True, padx=10, pady=8)
        bf = ttk.Frame(ff); bf.pack(fill='x', padx=15, pady=(0, 15))
        ttk.Button(bf, text="📂 Select Data File", style='Accent.TButton', command=self.select_file).pack(side='left', padx=(0, 10))
        ttk.Button(bf, text="🔄 Reload File", style='Modern.TButton', command=self.reload_file).pack(side='left', padx=(0, 10))
        ttk.Button(bf, text="📊 Quick Analysis", style='Success.TButton', command=self.quick_analysis).pack(side='right')
        prev_f = ttk.LabelFrame(mc, text="📊 Data Preview"); prev_f.pack(fill='both', expand=True, padx=10)
        self.create_data_preview(prev_f)

    def create_data_preview(self, parent):
        cols = ('Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3')
        self.data_tree = ttk.Treeview(parent, columns=cols, show='headings', height=12)
        for col in cols: self.data_tree.heading(col, text=col); self.data_tree.column(col, width=80, anchor='center')
        vsb, hsb = ttk.Scrollbar(parent, orient='v', command=self.data_tree.yview), ttk.Scrollbar(parent, orient='h', command=self.data_tree.xview)
        self.data_tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)
        self.data_tree.pack(side='left', fill='both', expand=True, padx=15, pady=15)
        vsb.pack(side='right', fill='y', pady=15); hsb.pack(side='bottom', fill='x', padx=15)
        self.bind_mousewheel(self.data_tree)
        info_f = ttk.Frame(parent); info_f.pack(side='bottom', fill='x', padx=15, pady=(0, 15))
        self.data_info_var = tk.StringVar(value="No data loaded")
        ttk.Label(info_f, textvariable=self.data_info_var, font=('Segoe UI', 10)).pack(anchor='w')

    def setup_analysis_tab(self):
        af = ttk.Frame(self.notebook, style='Modern.TFrame'); self.notebook.add(af, text="🔬 Analysis")
        mc = ttk.Frame(af, style='Modern.TFrame'); mc.pack(fill='both', expand=True, padx=20, pady=15)
        ttk.Label(mc, text="🔬 Pile Integrity Analysis (GZ Method)", style='Title.TLabel').pack(pady=(0, 20))
        
        gz_pf = ttk.LabelFrame(mc, text="⚙️ GZ Method Parameters"); gz_pf.pack(fill='x', pady=(0, 15), padx=10)
        rf = ttk.Frame(gz_pf); rf.pack(fill='x', padx=15, pady=15)
        ttk.Label(rf, text="默认 Bi/BD 比值:", style='Heading.TLabel').pack(side='left')
        ttk.Entry(rf, textvariable=self.config_vars['bi_ratio_default'], width=10, font=('Segoe UI', 9)).pack(side='left', padx=(10, 0))

        sys_sel_f = ttk.LabelFrame(mc, text="🚀 AI System Selection"); sys_sel_f.pack(fill='x', pady=(0, 15), padx=10)
        sys_f = ttk.Frame(sys_sel_f); sys_f.pack(fill='x', padx=15, pady=15)
        self.ai_system_var = tk.StringVar(value="v2" if self.use_v2_analyzer else "v1")
        ttk.Radiobutton(sys_f, text="🚀 AI System V2.0 (推荐) - 支持多模型管理和高精度分析", variable=self.ai_system_var, value="v2", command=self.switch_ai_system).pack(anchor='w', pady=2)
        ttk.Radiobutton(sys_f, text="🔧 AI System V1.0 (兼容) - 传统单模型系统", variable=self.ai_system_var, value="v1", command=self.switch_ai_system).pack(anchor='w', pady=2)

        self.v2_model_frame = ttk.LabelFrame(mc, text="🎯 AI Model Selection (V2.0)")
        msf = ttk.Frame(self.v2_model_frame); msf.pack(fill='x', padx=15, pady=15)
        ttk.Label(msf, text="选择AI模型:", style='Heading.TLabel').pack(side='left')
        self.selected_model_var = tk.StringVar()
        self.model_combobox = ttk.Combobox(msf, textvariable=self.selected_model_var, state='readonly', width=40)
        self.model_combobox.pack(side='left', padx=(10, 5), fill='x', expand=True)
        self.model_combobox.bind('<<ComboboxSelected>>', self.on_model_selected)
        ttk.Button(msf, text="📥 加载模型", style='Accent.TButton', command=self.load_external_model_v2).pack(side='right', padx=(5, 0))
        ttk.Button(msf, text="🔄 刷新", style='Modern.TButton', command=self.refresh_model_list).pack(side='right', padx=(5, 0))
        self.model_info_frame = ttk.Frame(self.v2_model_frame); self.model_info_frame.pack(fill='x', padx=15, pady=(0, 15))
        ef = ttk.Frame(self.v2_model_frame); ef.pack(fill='x', padx=15, pady=(0, 15))
        ttk.Label(ef, text="特征提取器:", style='Heading.TLabel').pack(side='left')
        self.selected_extractor_var = tk.StringVar()
        self.extractor_combobox = ttk.Combobox(ef, textvariable=self.selected_extractor_var, state='readonly', width=30)
        self.extractor_combobox.pack(side='left', padx=(10, 5)); self.extractor_combobox.bind('<<ComboboxSelected>>', self.on_extractor_selected)

        self.v1_model_frame = ttk.LabelFrame(mc, text="🤖 AI Model Configuration (V1.0)")
        mpc = ttk.Frame(self.v1_model_frame); mpc.pack(fill='x', padx=15, pady=15)
        ttk.Label(mpc, text="🤖 AI Model Path:", style='Heading.TLabel').pack(anchor='w', pady=(0, 5))
        mpf = ttk.Frame(mpc); mpf.pack(fill='x', pady=5)
        self.model_path_entry = ttk.Entry(mpf, textvariable=self.config_vars['ai_model_path'], width=50, font=('Segoe UI', 9))
        self.model_path_entry.pack(side='left', fill='x', expand=True)
        ttk.Button(mpf, text="📂 Browse", style='Modern.TButton', command=self.select_ai_model_for_analysis).pack(side='right', padx=(5, 0))
        ttk.Button(mpf, text="📥 Load Model", style='Accent.TButton', command=self.load_ai_model_for_analysis).pack(side='right', padx=(5, 0))
        tf = ttk.Frame(mpc); tf.pack(fill='x', pady=(10, 0))
        ttk.Button(tf, text="🔧 Train AI Model", style='Modern.TButton', command=self.train_ai_model).pack(side='left', padx=(0, 5))
        ttk.Button(tf, text="💾 Save Model", style='Modern.TButton', command=self.save_ai_model).pack(side='left', padx=(0, 5))
        sc = ttk.Frame(mpc); sc.pack(fill='x', pady=(5, 0))
        ttk.Label(sc, text="Model Status:", style='Heading.TLabel').pack(side='left')
        self.analysis_model_status_var = tk.StringVar(value="No model loaded")
        ttk.Label(sc, textvariable=self.analysis_model_status_var, font=('Segoe UI', 9), fg='gray').pack(side='left', padx=(10, 0))

        cf = ttk.LabelFrame(mc, text="🎮 Analysis Control"); cf.pack(fill='x', pady=(0, 20), padx=10)
        bf_ctrl = ttk.Frame(cf); bf_ctrl.pack(fill='x', padx=15, pady=15) # Renamed to avoid conflict
        ttk.Button(bf_ctrl, text="📊 GZ Traditional Analysis", style='Modern.TButton', command=self.run_gz_traditional_analysis).pack(side='left', padx=(0, 10))
        ttk.Button(bf_ctrl, text="🤖 AI Analysis", style='Accent.TButton', command=self.run_ai_analysis).pack(side='left', padx=(0, 10))
        ttk.Button(bf_ctrl, text="🔄 Run Both", style='Success.TButton', command=self.run_both_analyses).pack(side='left', padx=(0, 10))
        ttk.Button(bf_ctrl, text="💾 Save Results", style='Modern.TButton', command=self.save_results).pack(side='right')
        
        self.switch_ai_system()
        if self.use_v2_analyzer: self.refresh_model_list(); self.refresh_extractor_list(); self.update_model_info_display()

    def setup_traditional_results_tab(self):
        tf = ttk.Frame(self.notebook, style='Modern.TFrame'); self.notebook.add(tf, text="📊 GZ Traditional Analysis")
        mc = ttk.Frame(tf, style='Modern.TFrame'); mc.pack(fill='both', expand=True, padx=20, pady=15)
        ttk.Label(mc, text="📊 GZ Traditional Analysis Results", style='Title.TLabel').pack(pady=(0, 20))
        rf = ttk.LabelFrame(mc, text="📋 Analysis Results"); rf.pack(fill='both', expand=True, padx=10)
        self.traditional_text = tk.Text(rf, font=('Consolas', 10), wrap=tk.WORD, height=25, width=100)
        ts = ttk.Scrollbar(rf, orient='v', command=self.traditional_text.yview); self.traditional_text.configure(yscrollcommand=ts.set)
        self.traditional_text.pack(side='left', fill='both', expand=True, padx=15, pady=15); ts.pack(side='right', fill='y', pady=15)
        self.bind_mousewheel(self.traditional_text)
        self.traditional_text.insert(tk.END, "📊 GZ Traditional Analysis Results\n" + "=" * 50 + "\n\nNo analysis results yet.\n")

    def setup_ai_results_tab(self):
        aif = ttk.Frame(self.notebook, style='Modern.TFrame'); self.notebook.add(aif, text="🤖 AI Analysis")
        mc = ttk.Frame(aif, style='Modern.TFrame'); mc.pack(fill='both', expand=True, padx=20, pady=15)
        ttk.Label(mc, text="🤖 AI Enhanced Analysis Results", style='Title.TLabel').pack(pady=(0, 20))
        rf = ttk.LabelFrame(mc, text="📋 AI Analysis Results"); rf.pack(fill='both', expand=True, padx=10)
        self.ai_text = tk.Text(rf, font=('Consolas', 10), wrap=tk.WORD, height=25, width=100)
        ais = ttk.Scrollbar(rf, orient='v', command=self.ai_text.yview); self.ai_text.configure(yscrollcommand=ais.set)
        self.ai_text.pack(side='left', fill='both', expand=True, padx=15, pady=15); ais.pack(side='right', fill='y', pady=15)
        self.bind_mousewheel(self.ai_text)
        self.ai_text.insert(tk.END, "🤖 AI Enhanced Analysis Results\n" + "=" * 50 + "\n\nNo analysis results yet.\n")

    def setup_comparison_tab(self):
        cf = ttk.Frame(self.notebook, style='Modern.TFrame'); self.notebook.add(cf, text="⚖️ Comparison")
        mc = ttk.Frame(cf, style='Modern.TFrame'); mc.pack(fill='both', expand=True, padx=20, pady=15)
        ttk.Label(mc, text="⚖️ Comparative Analysis", style='Title.TLabel').pack(pady=(0, 20))
        rf = ttk.LabelFrame(mc, text="📋 Comparison Results"); rf.pack(fill='both', expand=True, padx=10)
        self.comparison_text = tk.Text(rf, font=('Consolas', 10), wrap=tk.WORD, height=25, width=100)
        cs = ttk.Scrollbar(rf, orient='v', command=self.comparison_text.yview); self.comparison_text.configure(yscrollcommand=cs.set)
        self.comparison_text.pack(side='left', fill='both', expand=True, padx=15, pady=15); cs.pack(side='right', fill='y', pady=15)
        self.bind_mousewheel(self.comparison_text)
        self.comparison_text.insert(tk.END, "⚖️ Comparative Analysis Results\n" + "=" * 50 + "\n\nNo comparison results yet.\n")

    def setup_visualization_tab(self):
        vf = ttk.Frame(self.notebook, style='Modern.TFrame'); self.notebook.add(vf, text="📈 Visualization")
        mc = ttk.Frame(vf, style='Modern.TFrame'); mc.pack(fill='both', expand=True, padx=20, pady=15)
        ttk.Label(mc, text="📈 Data Visualization", style='Title.TLabel').pack(pady=(0, 20))
        cf_viz = ttk.LabelFrame(mc, text="🎮 Visualization Controls"); cf_viz.pack(fill='x', pady=(0, 15), padx=10) # Renamed
        bf_viz = ttk.Frame(cf_viz); bf_viz.pack(fill='x', padx=15, pady=15) # Renamed
        ttk.Button(bf_viz, text="📊 Plot Data", style='Modern.TButton', command=self.plot_data).pack(side='left', padx=(0, 10))
        ttk.Button(bf_viz, text="📈 Plot Analysis Results", style='Accent.TButton', command=self.plot_analysis_results).pack(side='left', padx=(0, 10))
        ttk.Button(bf_viz, text="💾 Save Plot", style='Modern.TButton', command=self.save_plot).pack(side='right')
        vdf = ttk.LabelFrame(mc, text="📊 Plots"); vdf.pack(fill='both', expand=True, padx=10)
        self.fig = Figure(figsize=(12, 8), dpi=100)
        self.canvas = FigureCanvasTkAgg(self.fig, vdf); self.canvas.get_tk_widget().pack(fill='both', expand=True, padx=15, pady=15)
        from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk
        tbf = ttk.Frame(vdf); tbf.pack(fill='x', padx=15, pady=(0, 15))
        self.toolbar = NavigationToolbar2Tk(self.canvas, tbf); self.toolbar.update()

    def setup_config_tab(self):
        cfg_f = ttk.Frame(self.notebook, style='Modern.TFrame'); self.notebook.add(cfg_f, text="⚙️ Configuration")
        mc = ttk.Frame(cfg_f, style='Modern.TFrame'); mc.pack(fill='both', expand=True, padx=20, pady=15)
        ttk.Label(mc, text="⚙️ System Configuration", style='Title.TLabel').pack(pady=(0, 20))
        gz_cfg_f = ttk.LabelFrame(mc, text="🔧 GZ Method Thresholds"); gz_cfg_f.pack(fill='x', pady=(0, 15), padx=10)
        self.create_threshold_config(gz_cfg_f)
        gen_f = ttk.LabelFrame(mc, text="🎛️ General Settings"); gen_f.pack(fill='x', pady=(0, 15), padx=10)
        ttk.Checkbutton(gen_f, text="Auto-run analysis when data is loaded", variable=self.config_vars['auto_analysis']).pack(anchor='w', padx=15, pady=10)
        ttk.Checkbutton(gen_f, text="Show detailed analysis information", variable=self.config_vars['show_details']).pack(anchor='w', padx=15, pady=(0, 10))
        cfg_bf = ttk.Frame(mc); cfg_bf.pack(fill='x', pady=20, padx=10)
        ttk.Button(cfg_bf, text="💾 Save Configuration", style='Success.TButton', command=self.save_config).pack(side='left', padx=(0, 10))
        ttk.Button(cfg_bf, text="📥 Load Configuration", style='Modern.TButton', command=self.load_config).pack(side='left', padx=(0, 10))
        ttk.Button(cfg_bf, text="🔄 Reset to Defaults", style='Modern.TButton', command=self.reset_config).pack(side='right')

    def create_threshold_config(self, parent):
        cvs = tk.Canvas(parent); sb = ttk.Scrollbar(parent, orient="v", command=cvs.yview)
        sf = ttk.Frame(cvs); sf.bind("<Configure>", lambda e: cvs.configure(scrollregion=cvs.bbox("all")))
        cvs.create_window((0, 0), window=sf, anchor="nw"); cvs.configure(yscrollcommand=sb.set)
        cvs.pack(side="left", fill="both", expand=True, padx=15, pady=15); sb.pack(side="right", fill="y", pady=15)
        gf = ttk.Frame(sf); gf.pack(fill='x', padx=10, pady=10); r = 0
        ttk.Label(gf, text="Sp (%) 阈值配置:", style='Heading.TLabel').grid(row=r, column=0, columnspan=4, sticky='w', pady=(0,10)); r+=1
        ttk.Label(gf,text="sp ≥ 100%:").grid(row=r,column=0,sticky='w',padx=(20,5)); ttk.Entry(gf,textvariable=self.config_vars['sp_ge_100'],width=8).grid(row=r,column=1,padx=5); r+=1
        ttk.Label(gf,text="85% ≤ sp < 100%:").grid(row=r,column=0,sticky='w',padx=(20,5)); ttk.Label(gf,text="Min:").grid(row=r,column=1,sticky='w',padx=(10,2)); ttk.Entry(gf,textvariable=self.config_vars['sp_85_lt_100_min'],width=8).grid(row=r,column=2,padx=2); ttk.Label(gf,text="Max:").grid(row=r,column=3,sticky='w',padx=(10,2)); ttk.Entry(gf,textvariable=self.config_vars['sp_85_lt_100_max'],width=8).grid(row=r,column=4,padx=2); r+=1
        ttk.Label(gf,text="75% ≤ sp < 85%:").grid(row=r,column=0,sticky='w',padx=(20,5)); ttk.Label(gf,text="Min:").grid(row=r,column=1,sticky='w',padx=(10,2)); ttk.Entry(gf,textvariable=self.config_vars['sp_75_lt_85_min'],width=8).grid(row=r,column=2,padx=2); ttk.Label(gf,text="Max:").grid(row=r,column=3,sticky='w',padx=(10,2)); ttk.Entry(gf,textvariable=self.config_vars['sp_75_lt_85_max'],width=8).grid(row=r,column=4,padx=2); r+=1
        ttk.Label(gf,text="65% ≤ sp < 75%:").grid(row=r,column=0,sticky='w',padx=(20,5)); ttk.Label(gf,text="Min:").grid(row=r,column=1,sticky='w',padx=(10,2)); ttk.Entry(gf,textvariable=self.config_vars['sp_65_lt_75_min'],width=8).grid(row=r,column=2,padx=2); ttk.Label(gf,text="Max:").grid(row=r,column=3,sticky='w',padx=(10,2)); ttk.Entry(gf,textvariable=self.config_vars['sp_65_lt_75_max'],width=8).grid(row=r,column=4,padx=2); r+=2
        ttk.Label(gf,text="Ad (dB) 阈值配置:",style='Heading.TLabel').grid(row=r,column=0,columnspan=4,sticky='w',pady=(10,10)); r+=1
        ttk.Label(gf,text="ad ≤ 0:").grid(row=r,column=0,sticky='w',padx=(20,5)); ttk.Entry(gf,textvariable=self.config_vars['ad_le_0'],width=8).grid(row=r,column=1,padx=5); r+=1
        ttk.Label(gf,text="0 < ad ≤ 4:").grid(row=r,column=0,sticky='w',padx=(20,5)); ttk.Label(gf,text="Min:").grid(row=r,column=1,sticky='w',padx=(10,2)); ttk.Entry(gf,textvariable=self.config_vars['ad_gt_0_le_4_min'],width=8).grid(row=r,column=2,padx=2); ttk.Label(gf,text="Max:").grid(row=r,column=3,sticky='w',padx=(10,2)); ttk.Entry(gf,textvariable=self.config_vars['ad_gt_0_le_4_max'],width=8).grid(row=r,column=4,padx=2); r+=1
        ttk.Label(gf,text="4 < ad ≤ 8:").grid(row=r,column=0,sticky='w',padx=(20,5)); ttk.Label(gf,text="Min:").grid(row=r,column=1,sticky='w',padx=(10,2)); ttk.Entry(gf,textvariable=self.config_vars['ad_gt_4_le_8_min'],width=8).grid(row=r,column=2,padx=2); ttk.Label(gf,text="Max:").grid(row=r,column=3,sticky='w',padx=(10,2)); ttk.Entry(gf,textvariable=self.config_vars['ad_gt_4_le_8_max'],width=8).grid(row=r,column=4,padx=2); r+=1
        ttk.Label(gf,text="8 < ad ≤ 12:").grid(row=r,column=0,sticky='w',padx=(20,5)); ttk.Label(gf,text="Min:").grid(row=r,column=1,sticky='w',padx=(10,2)); ttk.Entry(gf,textvariable=self.config_vars['ad_gt_8_le_12_min'],width=8).grid(row=r,column=2,padx=2); ttk.Label(gf,text="Max:").grid(row=r,column=3,sticky='w',padx=(10,2)); ttk.Entry(gf,textvariable=self.config_vars['ad_gt_8_le_12_max'],width=8).grid(row=r,column=4,padx=2); r+=2
        ttk.Label(gf,text="Bi比值配置:",style='Heading.TLabel').grid(row=r,column=0,columnspan=4,sticky='w',pady=(10,10)); r+=1
        ttk.Label(gf,text="默认 Bi/BD 比值:").grid(row=r,column=0,sticky='w',padx=(20,5)); ttk.Entry(gf,textvariable=self.config_vars['bi_ratio_default'],width=8).grid(row=r,column=1,padx=5)
        self.bind_mousewheel(cvs) # Bind mousewheel to canvas for scrolling

    def select_file(self):
        fp = filedialog.askopenfilename(title="Select Pile Data File", filetypes=[("Text files", "*.txt"), ("CSV files", "*.csv"), ("All files", "*.*")])
        if fp: self.current_file = fp; self.file_path_var.set(fp); self.load_data_file(fp)

    def reload_file(self):
        if self.current_file: self.load_data_file(self.current_file)
        else: messagebox.showwarning("Warning", "No file selected to reload")

    def load_data_file(self, file_path):
        print(f"📁 Loading data file: {file_path}")
        try:
            self.data_df = self.parse_data_file(file_path)
            if self.data_df is None or self.data_df.empty:
                print("❌ Data loading failed or file is empty"); messagebox.showerror("Error", "Failed to load data or file is empty"); return
            print(f"✅ Data loaded: {self.data_df.shape}, Cols: {list(self.data_df.columns)}")
            self.update_data_preview()
            if self.config_vars['auto_analysis'].get(): print("🚀 Auto-running analysis..."); self.quick_analysis()
            print("🎉 Data loading completed")
        except Exception as e: print(f"❌ Data loading error: {e}"); traceback.print_exc(); messagebox.showerror("Error", f"Failed to load file: {e}")

    def parse_data_file(self, file_path):
        try:
            df = pd.read_csv(file_path, sep='\t', header=0)
            print(f"原始列名: {list(df.columns)}, Shape: {df.shape}")
            cmap = {}
            for col in df.columns:
                cl = col.lower().replace('_','').replace(' ','')
                if 'depth' in cl: cmap[col] = 'Depth'
                elif '1-2speed' in cl or '12speed' in cl: cmap[col] = 'S1'
                elif '1-2amp' in cl or '12amp' in cl: cmap[col] = 'A1'
                elif '1-3speed' in cl or '13speed' in cl: cmap[col] = 'S2'
                elif '1-3amp' in cl or '13amp' in cl: cmap[col] = 'A2'
                elif '2-3speed' in cl or '23speed' in cl: cmap[col] = 'S3'
                elif '2-3amp' in cl or '23amp' in cl: cmap[col] = 'A3'
            df.rename(columns=cmap, inplace=True); print(f"重命名后列名: {list(df.columns)}")
            req_cols = ['Depth','S1','A1','S2','A2','S3','A3']
            missing = [c for c in req_cols if c not in df.columns]
            if missing: print(f"警告：缺少列 {missing}"); # if len(missing)>3: return None
            for col in req_cols:
                if col in df.columns: df[col] = pd.to_numeric(df[col], errors='coerce')
            orig_len = len(df); df.dropna(inplace=True); print(f"删除缺失值后: {len(df)} 行 (原始: {orig_len})")
            return df
        except Exception as e: print(f"数据解析错误: {e}"); traceback.print_exc(); return None

    def update_data_preview(self):
        if self.data_df is None or self.data_df.empty: self.data_info_var.set("No data loaded"); return
        for item in self.data_tree.get_children(): self.data_tree.delete(item)
        disp_df = self.data_df.head(100)
        for _, row in disp_df.iterrows():
            vals = [f"{row[c]:.2f}" if pd.notnull(row[c]) else "N/A" for c in ['Depth','S1','A1','S2','A2','S3','A3'] if c in row.index]
            self.data_tree.insert('', 'end', values=vals)
        info = f"Loaded {len(self.data_df)} rows, {len(self.data_df.columns)} columns"
        if len(self.data_df) > 100: info += " (showing first 100 rows)"
        self.data_info_var.set(info)

    def quick_analysis(self):
        if self.data_df is None or self.data_df.empty: messagebox.showwarning("Warning", "Please load data first"); return
        self.run_gz_traditional_analysis()
        ai_sys_ver = self.ai_system_var.get()
        if ai_sys_ver == "v2" and self.use_v2_analyzer and self.ai_analyzer_v2 and self.ai_analyzer_v2.model_manager.current_model_key:
            self.run_ai_analysis()
        elif ai_sys_ver == "v1":
             mp = self.config_vars['ai_model_path'].get()
             if mp and os.path.exists(mp) or hasattr(self.ai_analyzer.classifier_model, 'classes_'): self.run_ai_analysis()
        if 'gz_traditional' in self.analysis_results and 'ai' in self.analysis_results: self.generate_comparison()

    def run_gz_traditional_analysis(self):
        print("🔬 Starting GZ traditional analysis...")
        if self.data_df is None or self.data_df.empty: print("❌ No data"); messagebox.showwarning("Warning", "Please load data first"); return
        try:
            print(f"📊 Data: {self.data_df.shape}, Cols: {list(self.data_df.columns)}")
            self.main_status_var.set("Running GZ traditional analysis..."); self.root.update()
            res = self.perform_gz_analysis()
            print(f"✅ GZ result: {res.get('final_category', 'N/A')}")
            self.analysis_results['gz_traditional'] = res; self.display_gz_traditional_result(res)
            self.main_status_var.set("GZ traditional analysis completed"); print("🎉 GZ traditional analysis completed")
        except Exception as e: print(f"❌ GZ error: {e}"); traceback.print_exc(); messagebox.showerror("Error", f"GZ analysis failed: {e}"); self.main_status_var.set("GZ analysis failed")

    def perform_gz_analysis(self):
        if self.data_df is None or self.data_df.empty: return None
        bi_r = self.config_vars['bi_ratio_default'].get(); gz_cfg = self.create_dynamic_gz_config()
        res = {'I_ji_values':{},'K_values':{},'final_category':None,'report_details':[],'analysis_summary':"",'detailed_analysis':{},'config_used':gz_cfg}
        for _, row in self.data_df.iterrows():
            d = row['Depth']; pdata = {'1-2':{'s':row['S1'],'a':row['A1']},'1-3':{'s':row['S2'],'a':row['A2']},'2-3':{'s':row['S3'],'a':row['A3']}}
            I_ji_d = {}
            for p, data in pdata.items():
                if pd.notnull(data['s']) and pd.notnull(data['a']): I_ji_d[p] = calculate_I_ji(data['s'],data['a'],bi_r,gz_cfg)
            if I_ji_d: res['K_values'][d] = calculate_K_i(list(I_ji_d.values())); res['I_ji_values'][d] = I_ji_d
        fc, rd = determine_final_category(res['K_values'])
        res['final_category'] = fc; res['report_details'] = rd; res['analysis_summary'] = self.generate_gz_analysis_summary(res)
        return res

    def create_dynamic_gz_config(self):
        # Helper to get config var value
        def get_v(key): return self.config_vars[key].get()
        return {
            'Sp_conditions': {
                'ge_100': lambda sp: sp >= get_v('sp_ge_100'), 
                '85_lt_100': lambda sp: get_v('sp_85_lt_100_min') <= sp < get_v('sp_85_lt_100_max'),
                '75_lt_85': lambda sp: get_v('sp_75_lt_85_min') <= sp < get_v('sp_75_lt_85_max'),
                '65_lt_75': lambda sp: get_v('sp_65_lt_75_min') <= sp < get_v('sp_65_lt_75_max'),
                'lt_65': lambda sp: sp < get_v('sp_65_lt_75_min'),
                'ge_85': lambda sp: sp >= get_v('sp_85_lt_100_min'), 
                'ge_75': lambda sp: sp >= get_v('sp_75_lt_85_min'),
                'ge_65': lambda sp: sp >= get_v('sp_65_lt_75_min')
            },
            'Ad_conditions': {
                'le_0': lambda ad: ad <= get_v('ad_le_0'),
                'gt_0_le_4': lambda ad: get_v('ad_gt_0_le_4_min') < ad <= get_v('ad_gt_0_le_4_max'),
                'gt_4_le_8': lambda ad: get_v('ad_gt_4_le_8_min') < ad <= get_v('ad_gt_4_le_8_max'),
                'gt_8_le_12': lambda ad: get_v('ad_gt_8_le_12_min') < ad <= get_v('ad_gt_8_le_12_max'),
                'gt_12': lambda ad: ad > get_v('ad_gt_8_le_12_max'), # Use max of previous range
                'le_4': lambda ad: ad <= get_v('ad_gt_0_le_4_max'),
                'le_8': lambda ad: ad <= get_v('ad_gt_4_le_8_max'),
                'le_12': lambda ad: ad <= get_v('ad_gt_8_le_12_max')
            },
            'Bi_ratio_conditions': DEFAULT_GZ_CONFIG['Bi_ratio_conditions'] # Keep Bi_ratio conditions standard
        }

    def generate_gz_analysis_summary(self, results):
        s = f"GZ方法桩基完整性分析结果\n" + "="*40 + f"\n\n最终判定: {results['final_category']}\n\n判定依据:\n"
        for d in results['report_details']: s += f"- {d}\n"
        s += f"\nK值分布统计:\n"
        if results['K_values']:
            kc = {}; 
            for kv in results['K_values'].values(): kc[kv] = kc.get(kv,0)+1
            for kv in sorted(kc.keys()): c=kc[kv]; p=(c/len(results['K_values']))*100; s+=f"K={kv}: {c}个截面 ({p:.1f}%)\n"
        s += f"\n总计分析截面: {len(results['K_values'])}个\n"
        return s

    def display_gz_traditional_result(self, result):
        print(f"📝 Displaying GZ. Result keys: {list(result.keys()) if result else 'None'}")
        if result is None: self.traditional_text.delete(1.0,tk.END); self.traditional_text.insert(tk.END,"No GZ results.\n"); return
        self.traditional_text.delete(1.0, tk.END)
        self.traditional_text.insert(tk.END, f"桩基完整性类别: {result.get('final_category', 'N/A')}\n\n")
        summary = result.get('analysis_summary', ''); 
        if summary: self.traditional_text.insert(tk.END, summary + "\n")
        self.traditional_text.insert(tk.END, "详细分析结果:\n" + "-"*50 + "\n")
        K_vals, I_ji_vals = result.get('K_values',{}), result.get('I_ji_values',{})
        for d in sorted(K_vals.keys()):
            self.traditional_text.insert(tk.END, f"深度 {d:.2f}m: K(i) = {K_vals[d]}\n")
            if d in I_ji_vals:
                for p, I_ji in I_ji_vals[d].items(): self.traditional_text.insert(tk.END, f"  剖面{p}: I(j,i) = {I_ji}\n")
            self.traditional_text.insert(tk.END, "\n")
        for i in range(self.notebook.index("end")):
            if self.notebook.tab(i,"text")=="📊 GZ Traditional Analysis": self.notebook.select(i); print("📋 Switched to GZ tab"); break
        self.traditional_text.see(1.0); print("✅ GZ result displayed")

    def select_ai_model_for_analysis(self):
        fp = filedialog.askopenfilename(title="Select AI Model File", filetypes=[("Pickle files","*.pkl"),("All files","*.*")], initialdir=self.ai_models_dir)
        if fp: self.config_vars['ai_model_path'].set(fp); self.analysis_model_status_var.set(f"Selected: {os.path.basename(fp)}")

    def load_ai_model_for_analysis(self):
        mp = self.config_vars['ai_model_path'].get()
        if not mp: messagebox.showwarning("Warning", "Please select an AI model file first"); return
        if not os.path.exists(mp): messagebox.showerror("Error", f"AI model file not found: {mp}"); return
        try:
            if self.ai_analyzer.load_models(mp):
                 self.analysis_model_status_var.set(f"✅ Loaded (V1): {os.path.basename(mp)}")
                 messagebox.showinfo("Success", "AI model (V1) loaded successfully!")
            else:
                 self.analysis_model_status_var.set("❌ Failed to load model (V1)")
                 messagebox.showerror("Error", "Failed to load AI model (V1). Check console.")
        except Exception as e: messagebox.showerror("Error",f"Failed to load AI model (V1): {e}"); traceback.print_exc(); self.analysis_model_status_var.set("❌ Failed to load (V1)")

    def train_ai_model(self):
        try:
            print("🔧 Starting AI model training (V1)...")
            resp = messagebox.askyesnocancel("AI Model Training (V1)", "Train with current data?\nYes: Current data\nNo: Synthetic data\nCancel: Cancel")
            if resp is None: return
            self.main_status_var.set("Training AI model (V1)..."); self.root.update()
            feats, labels, f_names = None, None, None
            if resp:
                if self.data_df is None or self.data_df.empty: messagebox.showwarning("Warning", "No data for V1 training. Using synthetic.")
                else:
                    feats, f_names = self.ai_analyzer.extract_features(self.data_df)
                    if feats.size == 0: messagebox.showwarning("Warning", "Failed to extract features. Using synthetic."); feats = None
                    elif 'gz_traditional' in self.analysis_results: labels = np.array([self.analysis_results['gz_traditional'].get('final_category','I类桩')] * len(feats))
                    else: messagebox.showinfo("Info", "No GZ analysis for labels. V1 training uses synthetic/unsupervised."); feats = None
            
            success = self.ai_analyzer.train_models(feats, labels)
            if success and f_names and hasattr(self.ai_analyzer.classifier_model, 'feature_importances_'):
                 if len(f_names) == len(self.ai_analyzer.classifier_model.feature_importances_):
                      self.ai_analyzer.feature_importance = dict(zip(f_names, self.ai_analyzer.classifier_model.feature_importances_))
            
            msg = "AI model (V1) trained successfully!" if success else "AI model training (V1) failed"
            status_msg = "AI model training (V1) completed" if success else "AI model training (V1) failed"
            self.main_status_var.set(status_msg); print(f"{'✅' if success else '❌'} {status_msg}")
            if success: messagebox.showinfo("Success", msg) 
            else: messagebox.showerror("Error", msg)
        except Exception as e: print(f"❌ AI train (V1) error: {e}"); traceback.print_exc(); messagebox.showerror("Error",f"AI train (V1) failed: {e}"); self.main_status_var.set("AI train (V1) failed")

    def save_ai_model(self):
        try:
            if self.ai_analyzer.classifier_model is None: messagebox.showwarning("Warning","No trained V1 model to save."); return
            fp = filedialog.asksaveasfilename(title="Save AI Model (V1)", defaultextension=".pkl", filetypes=[("Pickle files","*.pkl"),("All files","*.*")], initialdir=self.ai_models_dir)
            if fp:
                if self.ai_analyzer.save_models(fp): messagebox.showinfo("Success",f"AI model (V1) saved to {fp}"); print(f"✅ AI model (V1) saved: {fp}")
                else: messagebox.showerror("Error","Failed to save AI model (V1)")
        except Exception as e: print(f"❌ Save AI (V1) error: {e}"); traceback.print_exc(); messagebox.showerror("Error",f"Failed to save AI (V1): {e}")

    def run_ai_analysis(self):
        print("🤖 Starting AI analysis...")
        if self.data_df is None or self.data_df.empty: print("❌ No data"); messagebox.showwarning("Warning", "Please load data first"); return
        print(f"📊 Data for AI: {self.data_df.shape}, Cols: {list(self.data_df.columns)}")
        try:
            self.main_status_var.set("Running AI analysis..."); self.root.update()
            ai_sys_ver, result, cur_analyzer_name = self.ai_system_var.get(), None, "Unknown"

            if ai_sys_ver == "v2" and self.use_v2_analyzer and self.ai_analyzer_v2:
                print("🚀 Using AI System V2.0."); cur_analyzer_name = "AI System V2.0"
                if not self.ai_analyzer_v2.model_manager.current_model_key:
                     messagebox.showwarning("AI V2 Warning", "No AI V2 model selected/loaded."); self.main_status_var.set("AI V2: No model"); return
                result = self.ai_analyzer_v2.predict(self.data_df.copy())
            else:
                print("🔧 Using AI System V1.0 (BuiltInAIAnalyzer)."); cur_analyzer_name = "AI System V1.0"
                cur_analyzer = self.ai_analyzer; mp_v1 = self.config_vars['ai_model_path'].get()
                if mp_v1 and os.path.exists(mp_v1):
                    if cur_analyzer.loaded_model_path != mp_v1 :
                        print(f"📥 V1: Loading external model: {mp_v1}")
                        if not cur_analyzer.load_models(mp_v1): messagebox.showerror("V1 Error",f"Failed to load V1 model from {mp_v1}."); self.main_status_var.set("AI V1: Model load failed"); return
                elif not hasattr(cur_analyzer.classifier_model, 'classes_'):
                    print("⚠️ V1: No external model, built-in not trained. Training default."); cur_analyzer._initial_training()
                result = cur_analyzer.predict(self.data_df.copy())

            if result is None:
                print(f"❌ {cur_analyzer_name} prediction failed."); messagebox.showerror("Error",f"{cur_analyzer_name} prediction failed."); self.main_status_var.set(f"{cur_analyzer_name} failed"); return

            # Result '完整性类别' should now be 'I', 'II', 'III', or 'IV' from the analyzer
            print(f"✅ {cur_analyzer_name} result: {result.get('完整性类别', 'N/A')}, Confidence: {result.get('ai_confidence', 0.0):.2%}")
            self.analysis_results['ai'] = result; self.display_ai_result(result)
            self.main_status_var.set(f"{cur_analyzer_name} analysis completed"); print(f"🎉 {cur_analyzer_name} analysis completed")
        except Exception as e: print(f"❌ AI analysis error: {e}"); traceback.print_exc(); messagebox.showerror("Error",f"AI analysis failed: {e}"); self.main_status_var.set("AI analysis failed")

    def display_ai_result(self, result):
        print(f"📝 Displaying AI result. Keys: {list(result.keys()) if result else 'None'}")
        if result is None: self.ai_text.delete(1.0, tk.END); self.ai_text.insert(tk.END, "没有AI分析结果可显示。\n"); return
        
        self.ai_text.delete(1.0, tk.END)
        # '完整性类别' from analyzer is already 'I', 'II', 'III', or 'IV'
        ai_category_roman = str(result.get('完整性类别', 'N/A')) 
        
        self.ai_text.insert(tk.END, f"桩基完整性类别: {ai_category_roman}\n") # Display Roman numeral directly
        self.ai_text.insert(tk.END, f"AI置信度: {result.get('ai_confidence', 0.0):.2%}\n")
        self.ai_text.insert(tk.END, f"异常分数: {result.get('anomaly_score', 0.0):.2f}\n\n")
        self.ai_text.insert(tk.END, f"AI分析结论: {result.get('overall_reasoning', '无分析结论')}\n\n")
        
        self.ai_text.insert(tk.END, "各类别概率:\n")
        # class_probabilities keys from analyzer are 'I', 'II', 'III', 'IV'
        class_probabilities_roman = result.get('class_probabilities', {}) 
        for class_key_roman, prob in sorted(class_probabilities_roman.items()):
            self.ai_text.insert(tk.END, f"  {class_key_roman}: {prob:.2%}\n") # Display Roman numeral key directly
            
        self.ai_text.insert(tk.END, "\n特征重要性排名 (部分):\n")
        feat_imp = result.get('feature_importance', {})
        sorted_feats = sorted(feat_imp.items(), key=lambda x: x[1], reverse=True)
        for i, (feat, imp) in enumerate(sorted_feats[:10]):
            self.ai_text.insert(tk.END, f"  {i+1}. {feat}: {imp:.4f}\n")
        
        for i_tab in range(self.notebook.index("end")):
            if self.notebook.tab(i_tab, "text") == "🤖 AI Analysis": self.notebook.select(i_tab); print("📋 Switched to AI Analysis tab"); break
        self.ai_text.see(1.0); print("✅ AI result displayed")

    def run_both_analyses(self):
        if self.data_df is None or self.data_df.empty: messagebox.showwarning("Warning", "Please load data first"); return
        self.run_gz_traditional_analysis(); self.run_ai_analysis()
        if 'gz_traditional' in self.analysis_results and 'ai' in self.analysis_results: self.generate_comparison()

    def generate_comparison(self):
        print("⚖️ Generating comparison...")
        if 'gz_traditional' not in self.analysis_results or 'ai' not in self.analysis_results:
            print("❌ Both analyses needed for comparison"); return
        try:
            gz_res, ai_res = self.analysis_results['gz_traditional'], self.analysis_results['ai']
            comp_text = self.create_comparison_text(gz_res, ai_res)
            self.display_comparison_result(comp_text); print("✅ Comparison completed")
        except Exception as e: print(f"❌ Comparison error: {e}"); traceback.print_exc()

    def create_comparison_text(self, gz_result, ai_result):
        comp = "⚖️ GZ传统方法 vs AI分析 对比结果\n" + "=" * 60 + "\n\n"
        gz_category = gz_result.get('final_category', 'N/A') # e.g., "I类桩"
        # ai_result['完整性类别'] is already 'I', 'II', 'III', or 'IV'
        ai_category_roman = str(ai_result.get('完整性类别', 'N/A'))
        
        comp += f"GZ传统方法判定: {gz_category}\n"
        comp += f"AI分析判定: {ai_category_roman}\n" # Display Roman numeral

        # For consistency check, compare GZ's Roman part with AI's Roman numeral
        gz_category_roman_part = gz_category.replace('类桩', '') if isinstance(gz_category, str) else 'N/A'
        
        comp += f"判定一致性: {'✅ 一致' if gz_category_roman_part == ai_category_roman else '❌ 不一致'}\n\n"
        ai_conf = ai_result.get('ai_confidence', 0.0)
        comp += f"AI置信度: {ai_conf:.2%}\n\n详细对比分析:\n" + "-" * 40 + "\n"

        if gz_category_roman_part == ai_category_roman:
            comp += f"✅ 两种方法均判定为类别 {ai_category_roman} (或对应GZ的 {gz_category})，结果一致。\n"
            if ai_conf > 0.8: comp += "✅ AI分析置信度较高，结果可信度强。\n"
            elif ai_conf > 0.6: comp += "⚠️ AI分析置信度中等，建议结合传统方法综合判断。\n"
            else: comp += "⚠️ AI分析置信度较低，建议以传统方法为准。\n"
        else:
            comp += f"❌ 判定结果不一致：GZ方法为 {gz_category}，AI方法为 {ai_category_roman}。\n建议进一步分析原因：\n1. 检查数据质量\n2. 验证GZ参数\n3. 考虑AI模型适用性\n4. 结合工程经验判断\n"
        
        comp += f"\nGZ传统方法详情:\n"
        k_vals_gz = gz_result.get('K_values', {})
        if k_vals_gz:
            k_counts = {}; 
            for k_val in k_vals_gz.values(): k_counts[k_val] = k_counts.get(k_val, 0) + 1
            for k_val_disp in sorted(k_counts.keys()): # k_val_disp is numeric 1,2,3,4
                count = k_counts[k_val_disp]; perc = (count/len(k_vals_gz))*100
                comp += f"  K={k_val_disp}: {count}个截面 ({perc:.1f}%)\n" # Display numeric K for GZ
        
        comp += f"\nAI分析详情:\n"
        comp += f"  AI判定类别: {ai_category_roman}\n" # Display Roman numeral
        comp += f"  AI置信度: {ai_conf:.2%}\n"

        # ai_result['class_probabilities'] keys are 'I', 'II', 'III', 'IV'
        class_probs_ai_roman = ai_result.get('class_probabilities', {}) 
        if class_probs_ai_roman:
            comp += f"  各类别概率分布:\n"
            sorted_probs_ai = sorted(class_probs_ai_roman.items(), key=lambda item: item[1], reverse=True)
            for class_key_roman, prob_ai in sorted_probs_ai: # class_key_roman is 'I', 'II', etc.
                comp += f"    {class_key_roman}: {prob_ai:.2%}\n" # Display Roman numeral
        else: comp += "  各类别概率分布: 无数据\n"
        
        comp += f"\n分析建议:\n"
        if gz_category_roman_part != 'N/A' and ai_category_roman != 'N/A':
            if gz_category_roman_part == ai_category_roman:
                comp += "\n判定一致性: ✅ 一致\n"
                if ai_conf > 0.7: comp += "  建议: 建议采用一致的判定结果。\n"
                else: comp += "  建议: ⚠️ 虽然结果一致，但AI置信度不高，建议以GZ传统方法为主。\n"
            else:
                comp += "\n判定一致性: ❌ 不一致\n"
                comp += "  建议: \n   1. 优先考虑GZ传统方法结果\n   2. 分析数据质量和模型适用性\n   3. 结合现场实际情况综合判断\n"
        else:
            comp += "\n判定一致性: ⚠️ 无法比较 (数据不足)\n"
            comp += "  建议: 请确保GZ和AI分析均已执行并有有效结果。\n"
        return comp

    def display_comparison_result(self, comparison_text):
        print("📝 Displaying comparison result...")
        self.comparison_text.delete(1.0, tk.END); self.comparison_text.insert(tk.END, comparison_text)
        for i in range(self.notebook.index("end")):
            if self.notebook.tab(i,"text")=="⚖️ Comparison": self.notebook.select(i); print("📋 Switched to Comparison tab"); break
        self.comparison_text.see(1.0); print("✅ Comparison result displayed")

    def plot_data(self):
        if self.data_df is None or self.data_df.empty: messagebox.showwarning("Warning", "Please load data first"); return
        try:
            self.fig.clear(); ax1, ax2 = self.fig.add_subplot(211), self.fig.add_subplot(212)
            for i, p in enumerate(['S1','S2','S3']): ax1.plot(self.data_df['Depth'], self.data_df[p], label=f'Profile {i+1}', linewidth=1)
            ax1.set_xlabel('深度 (m)'); ax1.set_ylabel('声速 (%)'); ax1.set_title('声速随深度变化'); ax1.legend(); ax1.grid(True,alpha=0.3); ax1.invert_yaxis()
            for i, p in enumerate(['A1','A2','A3']): ax2.plot(self.data_df['Depth'], self.data_df[p], label=f'Profile {i+1}', linewidth=1)
            ax2.set_xlabel('深度 (m)'); ax2.set_ylabel('波幅 (dB)'); ax2.set_title('波幅随深度变化'); ax2.legend(); ax2.grid(True,alpha=0.3); ax2.invert_yaxis()
            self.fig.tight_layout(); self.canvas.draw(); print("✅ Data plot generated")
        except Exception as e: print(f"❌ Plot data error: {e}"); messagebox.showerror("Error",f"Failed to plot data: {e}")

    def plot_analysis_results(self):
        if 'gz_traditional' not in self.analysis_results: messagebox.showwarning("Warning", "Run GZ analysis first"); return
        try:
            self.fig.clear(); gz_res = self.analysis_results['gz_traditional']; K_vals = gz_res.get('K_values',{})
            if not K_vals: messagebox.showwarning("Warning", "No K values to plot"); return
            ax = self.fig.add_subplot(111); depths = sorted(K_vals.keys()); k_plot_vals = [K_vals[d] for d in depths]
            cols = {1:'green',2:'yellow',3:'orange',4:'red'}; p_cols = [cols.get(k,'gray') for k in k_plot_vals]
            ax.scatter(k_plot_vals, depths, c=p_cols, s=50, alpha=0.7)
            for k_cat_val in [1,2,3,4]: # k_cat_val is numeric
                k_depths_plot = [d for d, kv_plot in K_vals.items() if kv_plot == k_cat_val]
                if k_depths_plot:
                    for depth_val_plot in k_depths_plot: ax.axhline(y=depth_val_plot, color=cols[k_cat_val], alpha=0.3, linewidth=1)
            ax.set_xlabel('K值'); ax.set_ylabel('深度 (m)'); ax.set_title(f'K值分布图 - GZ判定: {gz_res.get("final_category","N/A")}')
            ax.set_xticks([1,2,3,4]); ax.grid(True,alpha=0.3); ax.invert_yaxis()
            leg_el = [plt.Line2D([0],[0],marker='o',color='w',markerfacecolor=cols[k_leg],markersize=8,label=f'K={k_leg}') for k_leg in [1,2,3,4]]
            ax.legend(handles=leg_el, loc='upper right')
            self.fig.tight_layout(); self.canvas.draw(); print("✅ Analysis plot generated")
        except Exception as e: print(f"❌ Plot analysis error: {e}"); messagebox.showerror("Error",f"Failed to plot analysis: {e}")

    def save_plot(self):
        try:
            fp = filedialog.asksaveasfilename(title="Save Plot", defaultextension=".png", filetypes=[("PNG","*.png"),("PDF","*.pdf"),("SVG","*.svg"),("All","*.*")])
            if fp: self.fig.savefig(fp,dpi=300,bbox_inches='tight'); messagebox.showinfo("Success",f"Plot saved to {fp}"); print(f"✅ Plot saved: {fp}")
        except Exception as e: print(f"❌ Save plot error: {e}"); messagebox.showerror("Error",f"Failed to save plot: {e}")

    def save_config(self):
        try:
            cfg_data = {k:v.get() for k,v in self.config_vars.items()}
            fp = filedialog.asksaveasfilename(title="Save Configuration", defaultextension=".json", filetypes=[("JSON","*.json"),("All","*.*")])
            if fp:
                with open(fp,'w',encoding='utf-8') as f: json.dump(cfg_data,f,indent=2,ensure_ascii=False)
                messagebox.showinfo("Success",f"Config saved to {fp}"); print(f"✅ Config saved: {fp}")
        except Exception as e: print(f"❌ Save config error: {e}"); messagebox.showerror("Error",f"Failed to save config: {e}")

    def load_config(self):
        try:
            fp = filedialog.askopenfilename(title="Load Configuration", filetypes=[("JSON","*.json"),("All","*.*")])
            if fp:
                with open(fp,'r',encoding='utf-8') as f: cfg_data = json.load(f)
                for k,v in cfg_data.items():
                    if k in self.config_vars: self.config_vars[k].set(v)
                messagebox.showinfo("Success",f"Config loaded from {fp}"); print(f"✅ Config loaded: {fp}")
        except Exception as e: print(f"❌ Load config error: {e}"); messagebox.showerror("Error",f"Failed to load config: {e}")

    def reset_config(self):
        try:
            defaults = { # Define default values here for clarity
                'sp_ge_100': 100.0, 'sp_85_lt_100_min': 85.0, 'sp_85_lt_100_max': 100.0,
                'sp_75_lt_85_min': 75.0, 'sp_75_lt_85_max': 85.0, 'sp_65_lt_75_min': 65.0,
                'sp_65_lt_75_max': 75.0, 'ad_le_0': 0.0, 'ad_gt_0_le_4_min': 0.0,
                'ad_gt_0_le_4_max': 4.0, 'ad_gt_4_le_8_min': 4.0, 'ad_gt_4_le_8_max': 8.0,
                'ad_gt_8_le_12_min': 8.0, 'ad_gt_8_le_12_max': 12.0, 'bi_ratio_default': 1.0,
                'auto_analysis': True, 'show_details': True, 'ai_model_path': ""
            }
            for key, value in defaults.items():
                if key in self.config_vars: self.config_vars[key].set(value)
            messagebox.showinfo("Success", "Configuration reset to defaults"); print("✅ Config reset")
        except Exception as e: print(f"❌ Reset config error: {e}"); messagebox.showerror("Error",f"Failed to reset config: {e}")

    def save_results(self):
        if not self.analysis_results: messagebox.showwarning("Warning", "No analysis results to save"); return
        try:
            fp = filedialog.asksaveasfilename(title="Save Analysis Results", defaultextension=".txt", filetypes=[("Text","*.txt"),("JSON","*.json"),("All","*.*")])
            if fp:
                if fp.endswith('.json'):
                    with open(fp,'w',encoding='utf-8') as f: json.dump(self.analysis_results,f,indent=2,ensure_ascii=False,default=str)
                else:
                    with open(fp,'w',encoding='utf-8') as f:
                        f.write("桩基完整性分析结果报告\n" + "="*50 + "\n\n")
                        if 'gz_traditional' in self.analysis_results: f.write("GZ传统方法分析结果:\n" + "-"*30 + "\n" + self.analysis_results['gz_traditional'].get('analysis_summary','') + "\n\n")
                        if 'ai' in self.analysis_results:
                            ai_res_save = self.analysis_results['ai']
                            # '完整性类别' is already 'I', 'II', 'III', 'IV'
                            ai_cat_roman_save = str(ai_res_save.get('完整性类别', 'N/A'))
                            f.write("AI分析结果:\n" + "-"*30 + "\n" + 
                                    f"桩基完整性类别: {ai_cat_roman_save}\n" + # Save Roman numeral directly
                                    f"AI置信度: {ai_res_save.get('ai_confidence',0.0):.2%}\n" +
                                    f"AI分析结论: {ai_res_save.get('overall_reasoning','无分析结论')}\n\n")
                messagebox.showinfo("Success",f"Results saved to {fp}"); print(f"✅ Results saved: {fp}")
        except Exception as e: print(f"❌ Save results error: {e}"); messagebox.showerror("Error",f"Failed to save results: {e}")

    def run(self): print("🚀 Starting Pile Analyzer GUI..."); self.root.mainloop()

    def switch_ai_system(self):
        try:
            sys_ver = self.ai_system_var.get()
            if sys_ver == "v2" and self.use_v2_analyzer:
                self.v2_model_frame.pack(fill='x',pady=(0,15),padx=10); self.v1_model_frame.pack_forget(); print("🚀 Switched to AI V2.0")
            else:
                self.v1_model_frame.pack(fill='x',pady=(0,20),padx=10); 
                if hasattr(self,'v2_model_frame'): self.v2_model_frame.pack_forget()
                print("🔧 Switched to AI V1.0 (or V2 unavailable)")
        except Exception as e: print(f"❌ Switch AI system error: {e}")

    def refresh_model_list(self):
        try:
            if not (self.use_v2_analyzer and self.ai_analyzer_v2): return
            models = self.ai_analyzer_v2.model_manager.get_available_models()
            model_names, self.model_key_mapping = [], {}
            for k, mi in models.items(): dn=f"{mi.name} ({mi.accuracy:.1%})"; model_names.append(dn); self.model_key_mapping[dn]=k
            self.model_combobox['values'] = model_names
            cur_mi = self.ai_analyzer_v2.model_manager.get_current_model_info()
            if cur_mi: cur_disp=f"{cur_mi.name} ({cur_mi.accuracy:.1%})"; 
            if cur_disp in model_names: self.selected_model_var.set(cur_disp)
            print(f"🔄 Models refreshed: {len(model_names)}")
        except Exception as e: print(f"❌ Refresh models error: {e}")

    def refresh_extractor_list(self):
        try:
            if not (self.use_v2_analyzer and self.ai_analyzer_v2): return
            extrs = self.ai_analyzer_v2.feature_manager.get_available_extractors()
            extr_names, self.extractor_key_mapping = [], {}
            for k, ei in extrs.items(): dn=f"{ei['name']} ({ei['feature_count']} feats)"; extr_names.append(dn); self.extractor_key_mapping[dn]=k
            self.extractor_combobox['values'] = extr_names
            cur_extr = self.ai_analyzer_v2.feature_manager.get_current_extractor()
            if cur_extr: cur_disp=f"{cur_extr.name} ({cur_extr.feature_count} feats)";
            if cur_disp in extr_names: self.selected_extractor_var.set(cur_disp)
            print(f"🔄 Extractors refreshed: {len(extr_names)}")
        except Exception as e: print(f"❌ Refresh extractors error: {e}")

    def on_model_selected(self, event=None):
        try:
            if not (self.use_v2_analyzer and self.ai_analyzer_v2): return
            sel_disp = self.selected_model_var.get()
            if sel_disp in self.model_key_mapping:
                mk = self.model_key_mapping[sel_disp]
                if self.ai_analyzer_v2.set_model(mk): print(f"✅ Model selected (V2): {sel_disp}"); self.update_model_info_display()
                else: print(f"❌ Model select failed (V2): {sel_disp}")
        except Exception as e: print(f"❌ Model select process error (V2): {e}")

    def on_extractor_selected(self, event=None):
        try:
            if not (self.use_v2_analyzer and self.ai_analyzer_v2): return
            sel_disp = self.selected_extractor_var.get()
            if sel_disp in self.extractor_key_mapping:
                ek = self.extractor_key_mapping[sel_disp]
                if self.ai_analyzer_v2.set_feature_extractor(ek): print(f"✅ Extractor selected (V2): {sel_disp}"); self.update_model_info_display()
                else: print(f"❌ Extractor select failed (V2): {sel_disp}")
        except Exception as e: print(f"❌ Extractor select process error (V2): {e}")

    def update_model_info_display(self):
        try:
            if not (self.use_v2_analyzer and self.ai_analyzer_v2): return
            for w in self.model_info_frame.winfo_children(): w.destroy()
            cur_mi = self.ai_analyzer_v2.model_manager.get_current_model_info()
            cur_extr = self.ai_analyzer_v2.feature_manager.get_current_extractor()
            if cur_mi and cur_extr:
                info_txt = f"📊 Current Config:\n  • Model: {cur_mi.name}\n  • Accuracy: {cur_mi.accuracy:.1%}\n  • Extractor: {cur_extr.name}\n  • Features: {cur_extr.feature_count}\n  • Type: {cur_mi.model_type}\n  • Size: {cur_mi.file_size/1024/1024:.1f} MB"
                ttk.Label(self.model_info_frame,text=info_txt,font=('Consolas',9),fg='darkgreen').pack(anchor='w')
                if cur_mi.feature_count != cur_extr.feature_count:
                    warn_txt=f"⚠️ Warning: Model expects {cur_mi.feature_count} features, extractor provides {cur_extr.feature_count}"
                    ttk.Label(self.model_info_frame,text=warn_txt,font=('Consolas',9),fg='red').pack(anchor='w',pady=(5,0))
                else: ttk.Label(self.model_info_frame,text="✅ Model & extractor compatible",font=('Consolas',9),fg='green').pack(anchor='w',pady=(5,0))
        except Exception as e: print(f"❌ Update model info error (V2): {e}")

    def load_external_model_v2(self):
        try:
            if not (self.use_v2_analyzer and self.ai_analyzer_v2): messagebox.showwarning("Warning","Switch to AI V2.0 first"); return
            fp = filedialog.askopenfilename(title="Select AI Model (V2)", filetypes=[("Pickle","*.pkl"),("All","*.*")], initialdir=self.ai_models_dir)
            if not fp: return
            print(f"📥 User selected model (V2): {fp}"); self._show_model_loading_dialog(fp)
        except Exception as e: print(f"❌ Load external (V2) error: {e}"); traceback.print_exc(); messagebox.showerror("Error",f"Load external (V2) error: {e}")

    def _show_model_loading_dialog(self, file_path):
        try:
            dlg = tk.Toplevel(self.root); dlg.title("Load External Model"); dlg.geometry("650x550"); dlg.resizable(True,True); dlg.transient(self.root); dlg.grab_set()
            dlg.update_idletasks(); x,y=(dlg.winfo_screenwidth()//2)-(650//2),(dlg.winfo_screenheight()//2)-(550//2); dlg.geometry(f"650x550+{x}+{y}")
            mf = ttk.Frame(dlg,padding="15"); mf.pack(fill='both',expand=True); mf.grid_rowconfigure(3,weight=1); mf.grid_columnconfigure(0,weight=1)
            ttk.Label(mf,text="🤖 Load External AI Model",font=('Segoe UI',14,'bold')).grid(row=0,column=0,pady=(0,15),sticky='w')
            ff_dlg = ttk.LabelFrame(mf,text="📁 File Info",padding="10"); ff_dlg.grid(row=1,column=0,sticky='ew',pady=(0,10)) # Renamed
            ttk.Label(ff_dlg,text=f"Path: {file_path}",wraplength=500).pack(anchor='w')
            ttk.Label(ff_dlg,text=f"Size: {os.path.getsize(file_path)/1024/1024:.1f} MB").pack(anchor='w')
            nf_dlg = ttk.Frame(mf); nf_dlg.grid(row=2,column=0,sticky='ew',pady=(0,10)) # Renamed
            ttk.Label(nf_dlg,text="Model Name:",font=('Segoe UI',10,'bold')).pack(anchor='w')
            mn_var = tk.StringVar(value=f"External - {os.path.basename(file_path)}")
            ttk.Entry(nf_dlg,textvariable=mn_var,width=60).pack(fill='x',pady=(5,0))
            pf_dlg = ttk.LabelFrame(mf,text="🔍 Model Preview",padding="10"); pf_dlg.grid(row=3,column=0,sticky='nsew',pady=(0,10)) # Renamed
            ptxt = tk.Text(pf_dlg,height=8,wrap='word',font=('Consolas',9))
            psb = ttk.Scrollbar(pf_dlg,orient='v',command=ptxt.yview); ptxt.configure(yscrollcommand=psb.set)
            ptxt.pack(side='left',fill='both',expand=True); psb.pack(side='right',fill='y')
            self._analyze_and_preview_model(file_path, ptxt)
            ttk.Separator(mf,orient='h').grid(row=4,column=0,sticky='ew',pady=(10,10))
            bf_dlg = ttk.Frame(mf); bf_dlg.grid(row=5,column=0,sticky='ew',pady=(0,5)) # Renamed
            def load_action():
                try:
                    mn = mn_var.get().strip()
                    if not mn: messagebox.showwarning("Warning","Enter model name"); return
                    print(f"🔄 Loading model (V2): {mn}")
                    if self.ai_analyzer_v2.model_manager.load_external_model(file_path,mn):
                        print(f"✅ Model loaded (V2), updating UI..."); self.refresh_model_list()
                        models_v2 = self.ai_analyzer_v2.model_manager.get_available_models() # Renamed
                        for k, mi_v2 in models_v2.items(): # Renamed
                            if mi_v2.name == mn: dn_v2=f"{mi_v2.name} ({mi_v2.accuracy:.1%})"; self.selected_model_var.set(dn_v2); self.on_model_selected(); break # Renamed
                        messagebox.showinfo("Success",f"Model '{mn}' (V2) loaded!\n\nAuto-selected.")
                        dlg.destroy()
                    else: messagebox.showerror("Error","Model load failed (V2). Check console.")
                except Exception as ex_load: print(f"❌ Model load error (V2): {ex_load}"); traceback.print_exc(); messagebox.showerror("Error",f"Model load error (V2): {ex_load}") # Renamed
            load_btn_dlg = ttk.Button(bf_dlg,text="✅ Confirm Load",style='Accent.TButton',command=load_action,width=15); load_btn_dlg.pack(side='right',padx=(10,0)) # Renamed
            ttk.Button(bf_dlg,text="❌ Cancel",style='Modern.TButton',command=dlg.destroy,width=15).pack(side='right')
            ttk.Label(bf_dlg,text="💡 Tip: Click 'Confirm Load' to finish.",font=('Segoe UI',9),fg='gray').pack(side='left')
            load_btn_dlg.focus_set(); dlg.bind('<Return>',lambda e: load_action())
            dlg.update_idletasks()
        except Exception as e_dlg: print(f"❌ Show load dialog error: {e_dlg}"); messagebox.showerror("Error",f"Show load dialog error: {e_dlg}") # Renamed

    def _analyze_and_preview_model(self, file_path, preview_text):
        try:
            preview_text.delete(1.0,tk.END); preview_text.insert(tk.END,"🔍 Analyzing model...\n\n"); preview_text.update()
            with open(file_path,'rb') as f: model_data = pickle.load(f)
            atxt = "📊 Model Analysis:\n"+"="*40+"\n\n"
            if isinstance(model_data,dict):
                if 'model' in model_data and ('preprocessor' in model_data or ('feature_extractor' in model_data and 'scaler' in model_data)):
                    atxt+="🚀 Type: Advanced/Optimized (includes preprocessing)\n"
                    if 'accuracy' in model_data: atxt+=f"📈 Stated Accuracy: {model_data['accuracy']:.1%}\n"
                    num_f=model_data.get('feature_count','Unknown'); atxt+=f"🔧 Features: {num_f}\n"
                    if 'preprocessor' in model_data: atxt+="⚡ Preprocessor: Included\n"
                    if 'feature_extractor' in model_data: atxt+="🔩 Feature Extractor: Included\n"
                    if 'scaler' in model_data: atxt+="⚖️ Scaler: Included\n"
                    atxt+="🎯 Recommended: High-accuracy analysis\n\n"
                elif 'classifier_model' in model_data:
                    atxt+="🔧 Type: Standard Full Model (V1 compatible)\n📈 Expected Accuracy: (depends on training)\n🔧 Features: (typically 54 standard)\n⚙️ Components: Classifier, maybe scaler/anomaly detector\n🎯 Recommended: Standard analysis (V1 system)\n\n"
                else: atxt+=f"❓ Type: Custom Dictionary\n📋 Components: {', '.join(model_data.keys())}\n\n"
            elif hasattr(model_data,'predict'):
                atxt+="🔧 Type: Single Classifier (Raw Scikit-learn)\n📈 Expected Accuracy: (depends on training)\n"
                if hasattr(model_data,'n_features_in_'): atxt+=f"🔧 Input Features (model expects): {model_data.n_features_in_}\n"
                else: atxt+="🔧 Input Features: Unknown (not provided by model)\n"
                atxt+="⚠️ Note: May need manual config/loading of extra preprocessing (e.g., scaler).\n🎯 Recommended: Basic analysis, or part of V2 system.\n\n"
                if hasattr(model_data,'__class__'): atxt+=f"🏷️ Model Class: {model_data.__class__.__name__}\n"
            else: atxt+="❌ Type: Unknown Format\n⚠️ Warning: May not be compatible\n\n"
            atxt+="🔍 Compatibility (V2 System):\n"+"-"*20+"\n  - V2 attempts auto-match/config of feature extractors.\n  - Advanced models ('feature_extractor'/'preprocessor') usually perform best with V2.\n  - Standard/single classifiers can load, but check feature compatibility.\n\n💡 Suggestion: After loading, check model info & extractor config in V2 system."
            preview_text.delete(1.0,tk.END); preview_text.insert(tk.END,atxt)
        except Exception as e_analyze: error_txt=f"❌ Model analysis failed: {e_analyze}\n\nPossible reasons:\n- Incorrect file format or corrupted\n- Not a valid pickle model file.\n\nPlease select a correct .pkl model file."; preview_text.delete(1.0,tk.END); preview_text.insert(tk.END,error_txt); traceback.print_exc() # Renamed

def main():
    try: app = PileAnalyzerGZGUI(); app.run()
    except Exception as e: print(f"❌ Application error: {str(e)}"); traceback.print_exc()

if __name__ == "__main__":
    main()
